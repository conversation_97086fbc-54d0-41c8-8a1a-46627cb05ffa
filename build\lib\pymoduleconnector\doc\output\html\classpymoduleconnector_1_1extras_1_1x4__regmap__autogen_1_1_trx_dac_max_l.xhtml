<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.extras.x4_regmap_autogen.TrxDacMaxL Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>extras</b></li><li class="navelem"><b>x4_regmap_autogen</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_l.xhtml">TrxDacMaxL</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_dac_max_l-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.extras.x4_regmap_autogen.TrxDacMaxL Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4bb42268122397913e06bbd07b9588b3"><td class="memItemLeft" align="right" valign="top"><a id="a4bb42268122397913e06bbd07b9588b3"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_dac_max_l</b> (cls, map, value)</td></tr>
<tr class="separator:a4bb42268122397913e06bbd07b9588b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acfd4f0c4e6ba675b1afbba2c7410bf21"><td class="memItemLeft" align="right" valign="top"><a id="acfd4f0c4e6ba675b1afbba2c7410bf21"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_dac_max_l</b> (cls, map)</td></tr>
<tr class="separator:acfd4f0c4e6ba675b1afbba2c7410bf21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8372bbf2e53dbd9daf66b71cd885401"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml#ac8372bbf2e53dbd9daf66b71cd885401">read</a> (cls, map)</td></tr>
<tr class="memdesc:ac8372bbf2e53dbd9daf66b71cd885401"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read register value from chip and update shadow value.  <a href="#ac8372bbf2e53dbd9daf66b71cd885401">More...</a><br /></td></tr>
<tr class="separator:ac8372bbf2e53dbd9daf66b71cd885401"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b8ae26ad49eff0ce06dd86722414978"><td class="memItemLeft" align="right" valign="top"><a id="a6b8ae26ad49eff0ce06dd86722414978"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml#a6b8ae26ad49eff0ce06dd86722414978">write</a> (cls, map, start, stop, size, value)</td></tr>
<tr class="memdesc:a6b8ae26ad49eff0ce06dd86722414978"><td class="mdescLeft">&#160;</td><td class="mdescRight">Update register value on chip based on new segment value. <br /></td></tr>
<tr class="separator:a6b8ae26ad49eff0ce06dd86722414978"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:aac7f7d8534d2605978160e265040dd43"><td class="memItemLeft" align="right" valign="top"><a id="aac7f7d8534d2605978160e265040dd43"></a>
string&#160;</td><td class="memItemRight" valign="bottom"><b>name</b> = &quot;trx_dac_max_l&quot;</td></tr>
<tr class="separator:aac7f7d8534d2605978160e265040dd43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0f2e2710aec7e900f66edcd48e852e7"><td class="memItemLeft" align="right" valign="top"><a id="ad0f2e2710aec7e900f66edcd48e852e7"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>address</b> = 49</td></tr>
<tr class="separator:ad0f2e2710aec7e900f66edcd48e852e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19e3b60c90436e56f3acead85741c540"><td class="memItemLeft" align="right" valign="top"><a id="a19e3b60c90436e56f3acead85741c540"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>default</b> = 7</td></tr>
<tr class="separator:a19e3b60c90436e56f3acead85741c540"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77bffc6809e98f5ad99819d153fe0169"><td class="memItemLeft" align="right" valign="top"><a id="a77bffc6809e98f5ad99819d153fe0169"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>action</b> = False</td></tr>
<tr class="separator:a77bffc6809e98f5ad99819d153fe0169"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafa99cb7dbd82645b9020abbbf937b2f"><td class="memItemLeft" align="right" valign="top"><a id="aafa99cb7dbd82645b9020abbbf937b2f"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>logger</b> = logging.getLogger(&quot;x4regmap.pif.trx_dac_max_l&quot;)</td></tr>
<tr class="separator:aafa99cb7dbd82645b9020abbbf937b2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac8372bbf2e53dbd9daf66b71cd885401"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8372bbf2e53dbd9daf66b71cd885401">&sect;&nbsp;</a></span>read()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.extras.regmap.Reg.read </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>cls</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>map</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Read register value from chip and update shadow value. </p>
<p>Return the new register value </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/extras/x4_regmap_autogen.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
