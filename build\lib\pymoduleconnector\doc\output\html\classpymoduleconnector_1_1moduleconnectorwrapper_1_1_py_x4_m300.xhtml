<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.PyX4M300 Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">PyX4M300</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyX4M300 Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>C++ includes: PyX4M300.hpp.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a873eeee2d3c2d5485ba82447a96c62cf"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a873eeee2d3c2d5485ba82447a96c62cf">__init__</a> (self, radar_interface)</td></tr>
<tr class="memdesc:a873eeee2d3c2d5485ba82447a96c62cf"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::PyX4M300 self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml" title="C++ includes: PyX4M300.hpp. ">PyX4M300</a>  <a href="#a873eeee2d3c2d5485ba82447a96c62cf">More...</a><br /></td></tr>
<tr class="separator:a873eeee2d3c2d5485ba82447a96c62cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2bee25ec205a5bf0eb06f356a092cc03"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2bee25ec205a5bf0eb06f356a092cc03">set_baudrate</a> (self, baudrate)</td></tr>
<tr class="memdesc:a2bee25ec205a5bf0eb06f356a092cc03"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_baudrate(PyX4M300 self, uint32_t baudrate)  <a href="#a2bee25ec205a5bf0eb06f356a092cc03">More...</a><br /></td></tr>
<tr class="separator:a2bee25ec205a5bf0eb06f356a092cc03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a085ffc4ab0640ab87c848059467d003f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a085ffc4ab0640ab87c848059467d003f">set_debug_level</a> (self, level)</td></tr>
<tr class="memdesc:a085ffc4ab0640ab87c848059467d003f"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_debug_level(PyX4M300 self, unsigned char level)  <a href="#a085ffc4ab0640ab87c848059467d003f">More...</a><br /></td></tr>
<tr class="separator:a085ffc4ab0640ab87c848059467d003f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4a434fb77c7b722316a7dfe9cb10f55"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ab4a434fb77c7b722316a7dfe9cb10f55">ping</a> (self)</td></tr>
<tr class="memdesc:ab4a434fb77c7b722316a7dfe9cb10f55"><td class="mdescLeft">&#160;</td><td class="mdescRight">ping(PyX4M300 self) -&gt; uint32_t  <a href="#ab4a434fb77c7b722316a7dfe9cb10f55">More...</a><br /></td></tr>
<tr class="separator:ab4a434fb77c7b722316a7dfe9cb10f55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a358163cbc1dd10fa30679d7985db4d8e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a358163cbc1dd10fa30679d7985db4d8e">get_system_info</a> (self, info_code)</td></tr>
<tr class="memdesc:a358163cbc1dd10fa30679d7985db4d8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_system_info(PyX4M300 self, uint8_t const info_code) -&gt; std::string  <a href="#a358163cbc1dd10fa30679d7985db4d8e">More...</a><br /></td></tr>
<tr class="separator:a358163cbc1dd10fa30679d7985db4d8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae708c7b6f659138aebca25cab6dd324f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae708c7b6f659138aebca25cab6dd324f">module_reset</a> (self)</td></tr>
<tr class="memdesc:ae708c7b6f659138aebca25cab6dd324f"><td class="mdescLeft">&#160;</td><td class="mdescRight">module_reset(PyX4M300 self)  <a href="#ae708c7b6f659138aebca25cab6dd324f">More...</a><br /></td></tr>
<tr class="separator:ae708c7b6f659138aebca25cab6dd324f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2209379a8d5e0c4d24032b8d1a1ea5fd"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2209379a8d5e0c4d24032b8d1a1ea5fd">reset</a> (self)</td></tr>
<tr class="memdesc:a2209379a8d5e0c4d24032b8d1a1ea5fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">reset(PyX4M300 self) -&gt; int  <a href="#a2209379a8d5e0c4d24032b8d1a1ea5fd">More...</a><br /></td></tr>
<tr class="separator:a2209379a8d5e0c4d24032b8d1a1ea5fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac26286f535f1d4a628ceec852b8a551f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ac26286f535f1d4a628ceec852b8a551f">reset_to_factory_preset</a> (self)</td></tr>
<tr class="memdesc:ac26286f535f1d4a628ceec852b8a551f"><td class="mdescLeft">&#160;</td><td class="mdescRight">reset_to_factory_preset(PyX4M300 self)  <a href="#ac26286f535f1d4a628ceec852b8a551f">More...</a><br /></td></tr>
<tr class="separator:ac26286f535f1d4a628ceec852b8a551f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4f26481fe14f8b2d18664d87a4b84b3"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae4f26481fe14f8b2d18664d87a4b84b3">start_bootloader</a> (self)</td></tr>
<tr class="memdesc:ae4f26481fe14f8b2d18664d87a4b84b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">start_bootloader(PyX4M300 self)  <a href="#ae4f26481fe14f8b2d18664d87a4b84b3">More...</a><br /></td></tr>
<tr class="separator:ae4f26481fe14f8b2d18664d87a4b84b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11942b3a8832197300bd0fa041a9b941"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a11942b3a8832197300bd0fa041a9b941">inject_frame</a> (self, frame_counter, frame_length, frame)</td></tr>
<tr class="memdesc:a11942b3a8832197300bd0fa041a9b941"><td class="mdescLeft">&#160;</td><td class="mdescRight">inject_frame(PyX4M300 self, uint32_t frame_counter, uint32_t frame_length, FloatVector frame)  <a href="#a11942b3a8832197300bd0fa041a9b941">More...</a><br /></td></tr>
<tr class="separator:a11942b3a8832197300bd0fa041a9b941"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1981310bed01af00c2a94b438100cd7"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae1981310bed01af00c2a94b438100cd7">prepare_inject_frame</a> (self, num_frames, num_bins, mode)</td></tr>
<tr class="memdesc:ae1981310bed01af00c2a94b438100cd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">prepare_inject_frame(PyX4M300 self, uint32_t num_frames, uint32_t num_bins, uint32_t mode)  <a href="#ae1981310bed01af00c2a94b438100cd7">More...</a><br /></td></tr>
<tr class="separator:ae1981310bed01af00c2a94b438100cd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a142cc1bff05a309bca9188a647a5cbe7"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a142cc1bff05a309bca9188a647a5cbe7">system_run_test</a> (self, testcode, data)</td></tr>
<tr class="memdesc:a142cc1bff05a309bca9188a647a5cbe7"><td class="mdescLeft">&#160;</td><td class="mdescRight">system_run_test(PyX4M300 self, uint8_t const testcode, ucVector data) -&gt; int  <a href="#a142cc1bff05a309bca9188a647a5cbe7">More...</a><br /></td></tr>
<tr class="separator:a142cc1bff05a309bca9188a647a5cbe7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a745a53c0deebad64c6c2d7d6ea9fc0bb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a745a53c0deebad64c6c2d7d6ea9fc0bb">load_profile</a> (self, profileid)</td></tr>
<tr class="memdesc:a745a53c0deebad64c6c2d7d6ea9fc0bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">load_profile(PyX4M300 self, uint32_t const profileid)  <a href="#a745a53c0deebad64c6c2d7d6ea9fc0bb">More...</a><br /></td></tr>
<tr class="separator:a745a53c0deebad64c6c2d7d6ea9fc0bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a330df328fb76fd7901e76311131a3863"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a330df328fb76fd7901e76311131a3863">set_sensor_mode</a> (self, mode, param)</td></tr>
<tr class="memdesc:a330df328fb76fd7901e76311131a3863"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_sensor_mode(PyX4M300 self, uint8_t const mode, uint8_t const param)  <a href="#a330df328fb76fd7901e76311131a3863">More...</a><br /></td></tr>
<tr class="separator:a330df328fb76fd7901e76311131a3863"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a660e47c44813d47ca034cda75b5e4d95"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a660e47c44813d47ca034cda75b5e4d95">get_sensor_mode</a> (self)</td></tr>
<tr class="memdesc:a660e47c44813d47ca034cda75b5e4d95"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_sensor_mode(PyX4M300 self) -&gt; uint8_t  <a href="#a660e47c44813d47ca034cda75b5e4d95">More...</a><br /></td></tr>
<tr class="separator:a660e47c44813d47ca034cda75b5e4d95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe4daf3e2750c49421e4c0cb193d7eb8"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afe4daf3e2750c49421e4c0cb193d7eb8">set_sensitivity</a> (self, sensitivity)</td></tr>
<tr class="memdesc:afe4daf3e2750c49421e4c0cb193d7eb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_sensitivity(PyX4M300 self, uint32_t const sensitivity)  <a href="#afe4daf3e2750c49421e4c0cb193d7eb8">More...</a><br /></td></tr>
<tr class="separator:afe4daf3e2750c49421e4c0cb193d7eb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a093c0d01728225c5ebb766d09c279d9c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a093c0d01728225c5ebb766d09c279d9c">get_sensitivity</a> (self)</td></tr>
<tr class="memdesc:a093c0d01728225c5ebb766d09c279d9c"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_sensitivity(PyX4M300 self) -&gt; uint32_t  <a href="#a093c0d01728225c5ebb766d09c279d9c">More...</a><br /></td></tr>
<tr class="separator:a093c0d01728225c5ebb766d09c279d9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30800f99e044d7f87c688706254b1cae"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a30800f99e044d7f87c688706254b1cae">set_tx_center_frequency</a> (self, frequency_band)</td></tr>
<tr class="memdesc:a30800f99e044d7f87c688706254b1cae"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_tx_center_frequency(PyX4M300 self, uint32_t const frequency_band)  <a href="#a30800f99e044d7f87c688706254b1cae">More...</a><br /></td></tr>
<tr class="separator:a30800f99e044d7f87c688706254b1cae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae345e0319492b7b10d70e69b6c5a6b5c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae345e0319492b7b10d70e69b6c5a6b5c">get_tx_center_frequency</a> (self)</td></tr>
<tr class="memdesc:ae345e0319492b7b10d70e69b6c5a6b5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_tx_center_frequency(PyX4M300 self) -&gt; uint32_t  <a href="#ae345e0319492b7b10d70e69b6c5a6b5c">More...</a><br /></td></tr>
<tr class="separator:ae345e0319492b7b10d70e69b6c5a6b5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbf23c82bc1db62ea2960e16a1355040"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#adbf23c82bc1db62ea2960e16a1355040">set_detection_zone</a> (self, start, end)</td></tr>
<tr class="memdesc:adbf23c82bc1db62ea2960e16a1355040"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_detection_zone(PyX4M300 self, float const start, float const end)  <a href="#adbf23c82bc1db62ea2960e16a1355040">More...</a><br /></td></tr>
<tr class="separator:adbf23c82bc1db62ea2960e16a1355040"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9bb9b53f33af8a80d06d353893ca181"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aa9bb9b53f33af8a80d06d353893ca181">get_detection_zone</a> (self)</td></tr>
<tr class="memdesc:aa9bb9b53f33af8a80d06d353893ca181"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_detection_zone(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a>  <a href="#aa9bb9b53f33af8a80d06d353893ca181">More...</a><br /></td></tr>
<tr class="separator:aa9bb9b53f33af8a80d06d353893ca181"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd9819f3280ad70c955acfdf0e04504a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#acd9819f3280ad70c955acfdf0e04504a">get_detection_zone_limits</a> (self)</td></tr>
<tr class="memdesc:acd9819f3280ad70c955acfdf0e04504a"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_detection_zone_limits(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a>  <a href="#acd9819f3280ad70c955acfdf0e04504a">More...</a><br /></td></tr>
<tr class="separator:acd9819f3280ad70c955acfdf0e04504a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a083234422dfa614a412b9f42b30ac6e5"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a083234422dfa614a412b9f42b30ac6e5">set_led_control</a> (self, mode, intensity)</td></tr>
<tr class="memdesc:a083234422dfa614a412b9f42b30ac6e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_led_control(PyX4M300 self, uint8_t const mode, uint8_t intensity)  <a href="#a083234422dfa614a412b9f42b30ac6e5">More...</a><br /></td></tr>
<tr class="separator:a083234422dfa614a412b9f42b30ac6e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a222ce51e298e12567df5fd366cfab713"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a222ce51e298e12567df5fd366cfab713">get_led_control</a> (self)</td></tr>
<tr class="memdesc:a222ce51e298e12567df5fd366cfab713"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_led_control(PyX4M300 self) -&gt; uint32_t  <a href="#a222ce51e298e12567df5fd366cfab713">More...</a><br /></td></tr>
<tr class="separator:a222ce51e298e12567df5fd366cfab713"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa5f8502b525d703418b815c08c767a4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afa5f8502b525d703418b815c08c767a4">set_output_control</a> (self, output_feature, output_control)</td></tr>
<tr class="memdesc:afa5f8502b525d703418b815c08c767a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_output_control(PyX4M300 self, uint32_t output_feature, uint32_t output_control)  <a href="#afa5f8502b525d703418b815c08c767a4">More...</a><br /></td></tr>
<tr class="separator:afa5f8502b525d703418b815c08c767a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45b0182d2b879249eb564ea193b2d82e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a45b0182d2b879249eb564ea193b2d82e">set_debug_output_control</a> (self, output_feature, output_control)</td></tr>
<tr class="memdesc:a45b0182d2b879249eb564ea193b2d82e"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_debug_output_control(PyX4M300 self, uint32_t output_feature, uint32_t output_control)  <a href="#a45b0182d2b879249eb564ea193b2d82e">More...</a><br /></td></tr>
<tr class="separator:a45b0182d2b879249eb564ea193b2d82e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba7010d4b3206095c85180406e3b220b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aba7010d4b3206095c85180406e3b220b">get_output_control</a> (self, output_feature)</td></tr>
<tr class="memdesc:aba7010d4b3206095c85180406e3b220b"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_output_control(PyX4M300 self, uint32_t const output_feature) -&gt; uint32_t  <a href="#aba7010d4b3206095c85180406e3b220b">More...</a><br /></td></tr>
<tr class="separator:aba7010d4b3206095c85180406e3b220b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b4a0068b83623251b89cdfefe508e08"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a5b4a0068b83623251b89cdfefe508e08">get_debug_output_control</a> (self, output_feature)</td></tr>
<tr class="memdesc:a5b4a0068b83623251b89cdfefe508e08"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_debug_output_control(PyX4M300 self, uint32_t const output_feature) -&gt; uint32_t  <a href="#a5b4a0068b83623251b89cdfefe508e08">More...</a><br /></td></tr>
<tr class="separator:a5b4a0068b83623251b89cdfefe508e08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4031dfc7c29c40615a2f947752e9a6e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ac4031dfc7c29c40615a2f947752e9a6e">peek_message_presence_single</a> (self)</td></tr>
<tr class="memdesc:ac4031dfc7c29c40615a2f947752e9a6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_presence_single(PyX4M300 self) -&gt; int  <a href="#ac4031dfc7c29c40615a2f947752e9a6e">More...</a><br /></td></tr>
<tr class="separator:ac4031dfc7c29c40615a2f947752e9a6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa2ada346650ef99eb0fe9ea96d690e3"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aaa2ada346650ef99eb0fe9ea96d690e3">read_message_presence_single</a> (self)</td></tr>
<tr class="memdesc:aaa2ada346650ef99eb0fe9ea96d690e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_presence_single(PyX4M300 self) -&gt; PresenceSingleData  <a href="#aaa2ada346650ef99eb0fe9ea96d690e3">More...</a><br /></td></tr>
<tr class="separator:aaa2ada346650ef99eb0fe9ea96d690e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47634fb51ba9c6574478bcbb8a3bb7f7"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a47634fb51ba9c6574478bcbb8a3bb7f7">peek_message_presence_movinglist</a> (self)</td></tr>
<tr class="memdesc:a47634fb51ba9c6574478bcbb8a3bb7f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_presence_movinglist(PyX4M300 self) -&gt; int  <a href="#a47634fb51ba9c6574478bcbb8a3bb7f7">More...</a><br /></td></tr>
<tr class="separator:a47634fb51ba9c6574478bcbb8a3bb7f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4285cf9d408eaca6ee5d67b3e0d33dcc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a4285cf9d408eaca6ee5d67b3e0d33dcc">read_message_presence_movinglist</a> (self)</td></tr>
<tr class="memdesc:a4285cf9d408eaca6ee5d67b3e0d33dcc"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_presence_movinglist(PyX4M300 self) -&gt; PresenceMovingListData  <a href="#a4285cf9d408eaca6ee5d67b3e0d33dcc">More...</a><br /></td></tr>
<tr class="separator:a4285cf9d408eaca6ee5d67b3e0d33dcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27b4ecdc8039088f0cd8e97ce26296f6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a27b4ecdc8039088f0cd8e97ce26296f6">peek_message_baseband_ap</a> (self)</td></tr>
<tr class="memdesc:a27b4ecdc8039088f0cd8e97ce26296f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_baseband_ap(PyX4M300 self) -&gt; int  <a href="#a27b4ecdc8039088f0cd8e97ce26296f6">More...</a><br /></td></tr>
<tr class="separator:a27b4ecdc8039088f0cd8e97ce26296f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb74a781f50c83a6c79ee9647c6f37a2"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aeb74a781f50c83a6c79ee9647c6f37a2">read_message_baseband_ap</a> (self)</td></tr>
<tr class="memdesc:aeb74a781f50c83a6c79ee9647c6f37a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_baseband_ap(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a>  <a href="#aeb74a781f50c83a6c79ee9647c6f37a2">More...</a><br /></td></tr>
<tr class="separator:aeb74a781f50c83a6c79ee9647c6f37a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a100dc5ca302556fe28878a50747118ac"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a100dc5ca302556fe28878a50747118ac">peek_message_baseband_iq</a> (self)</td></tr>
<tr class="memdesc:a100dc5ca302556fe28878a50747118ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_baseband_iq(PyX4M300 self) -&gt; int  <a href="#a100dc5ca302556fe28878a50747118ac">More...</a><br /></td></tr>
<tr class="separator:a100dc5ca302556fe28878a50747118ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37a8e78bd1339fe62929623df1870012"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a37a8e78bd1339fe62929623df1870012">read_message_baseband_iq</a> (self)</td></tr>
<tr class="memdesc:a37a8e78bd1339fe62929623df1870012"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_baseband_iq(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a>  <a href="#a37a8e78bd1339fe62929623df1870012">More...</a><br /></td></tr>
<tr class="separator:a37a8e78bd1339fe62929623df1870012"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28779e4c60bea75906484477d72828f5"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a28779e4c60bea75906484477d72828f5">peek_message_pulsedoppler_float</a> (self)</td></tr>
<tr class="memdesc:a28779e4c60bea75906484477d72828f5"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_pulsedoppler_float(PyX4M300 self) -&gt; int  <a href="#a28779e4c60bea75906484477d72828f5">More...</a><br /></td></tr>
<tr class="separator:a28779e4c60bea75906484477d72828f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f4644b3a1d1832dea72d8b1aedb8372"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a7f4644b3a1d1832dea72d8b1aedb8372">read_message_pulsedoppler_float</a> (self)</td></tr>
<tr class="memdesc:a7f4644b3a1d1832dea72d8b1aedb8372"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_pulsedoppler_float(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a>  <a href="#a7f4644b3a1d1832dea72d8b1aedb8372">More...</a><br /></td></tr>
<tr class="separator:a7f4644b3a1d1832dea72d8b1aedb8372"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d0a552e1170fffeea418c4834860b2"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a56d0a552e1170fffeea418c4834860b2">peek_message_pulsedoppler_byte</a> (self)</td></tr>
<tr class="memdesc:a56d0a552e1170fffeea418c4834860b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_pulsedoppler_byte(PyX4M300 self) -&gt; int  <a href="#a56d0a552e1170fffeea418c4834860b2">More...</a><br /></td></tr>
<tr class="separator:a56d0a552e1170fffeea418c4834860b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8df10ebe802cdd6e025148c0a4000f7d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a8df10ebe802cdd6e025148c0a4000f7d">read_message_pulsedoppler_byte</a> (self)</td></tr>
<tr class="memdesc:a8df10ebe802cdd6e025148c0a4000f7d"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_pulsedoppler_byte(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a>  <a href="#a8df10ebe802cdd6e025148c0a4000f7d">More...</a><br /></td></tr>
<tr class="separator:a8df10ebe802cdd6e025148c0a4000f7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a946c49a6da188672d2eb757895f1eb0f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a946c49a6da188672d2eb757895f1eb0f">peek_message_noisemap_float</a> (self)</td></tr>
<tr class="memdesc:a946c49a6da188672d2eb757895f1eb0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_noisemap_float(PyX4M300 self) -&gt; int  <a href="#a946c49a6da188672d2eb757895f1eb0f">More...</a><br /></td></tr>
<tr class="separator:a946c49a6da188672d2eb757895f1eb0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a393fb80799ba2a37d5904d64c874bc39"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a393fb80799ba2a37d5904d64c874bc39">read_message_noisemap_float</a> (self)</td></tr>
<tr class="memdesc:a393fb80799ba2a37d5904d64c874bc39"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_noisemap_float(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a>  <a href="#a393fb80799ba2a37d5904d64c874bc39">More...</a><br /></td></tr>
<tr class="separator:a393fb80799ba2a37d5904d64c874bc39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbeba432b3601d3740dbee40f97bb603"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#adbeba432b3601d3740dbee40f97bb603">peek_message_noisemap_byte</a> (self)</td></tr>
<tr class="memdesc:adbeba432b3601d3740dbee40f97bb603"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_noisemap_byte(PyX4M300 self) -&gt; int  <a href="#adbeba432b3601d3740dbee40f97bb603">More...</a><br /></td></tr>
<tr class="separator:adbeba432b3601d3740dbee40f97bb603"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a165b9c2579ddf56c6e61071e661e19e1"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a165b9c2579ddf56c6e61071e661e19e1">read_message_noisemap_byte</a> (self)</td></tr>
<tr class="memdesc:a165b9c2579ddf56c6e61071e661e19e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_noisemap_byte(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a>  <a href="#a165b9c2579ddf56c6e61071e661e19e1">More...</a><br /></td></tr>
<tr class="separator:a165b9c2579ddf56c6e61071e661e19e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd06c82f6163c587f6e07dc4f3ee2e8a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afd06c82f6163c587f6e07dc4f3ee2e8a">load_noisemap</a> (self)</td></tr>
<tr class="memdesc:afd06c82f6163c587f6e07dc4f3ee2e8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">load_noisemap(PyX4M300 self)  <a href="#afd06c82f6163c587f6e07dc4f3ee2e8a">More...</a><br /></td></tr>
<tr class="separator:afd06c82f6163c587f6e07dc4f3ee2e8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05f0c89662aa753db403fb36f7587a4f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a05f0c89662aa753db403fb36f7587a4f">store_noisemap</a> (self)</td></tr>
<tr class="memdesc:a05f0c89662aa753db403fb36f7587a4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">store_noisemap(PyX4M300 self)  <a href="#a05f0c89662aa753db403fb36f7587a4f">More...</a><br /></td></tr>
<tr class="separator:a05f0c89662aa753db403fb36f7587a4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a078e8eb5598cc7c38b7822f07bd5f989"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a078e8eb5598cc7c38b7822f07bd5f989">delete_noisemap</a> (self)</td></tr>
<tr class="memdesc:a078e8eb5598cc7c38b7822f07bd5f989"><td class="mdescLeft">&#160;</td><td class="mdescRight">delete_noisemap(PyX4M300 self)  <a href="#a078e8eb5598cc7c38b7822f07bd5f989">More...</a><br /></td></tr>
<tr class="separator:a078e8eb5598cc7c38b7822f07bd5f989"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58bd7bc23687822d42384078ed77d775"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a58bd7bc23687822d42384078ed77d775">set_noisemap_control</a> (self, noisemap_control)</td></tr>
<tr class="memdesc:a58bd7bc23687822d42384078ed77d775"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_noisemap_control(PyX4M300 self, uint32_t noisemap_control)  <a href="#a58bd7bc23687822d42384078ed77d775">More...</a><br /></td></tr>
<tr class="separator:a58bd7bc23687822d42384078ed77d775"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add9b5c1f49d7a2683f63ba8ed73c9329"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#add9b5c1f49d7a2683f63ba8ed73c9329">get_noisemap_control</a> (self)</td></tr>
<tr class="memdesc:add9b5c1f49d7a2683f63ba8ed73c9329"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_noisemap_control(PyX4M300 self) -&gt; uint32_t  <a href="#add9b5c1f49d7a2683f63ba8ed73c9329">More...</a><br /></td></tr>
<tr class="separator:add9b5c1f49d7a2683f63ba8ed73c9329"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07a6814f052efd83fe3d61ce96448375"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a07a6814f052efd83fe3d61ce96448375">set_periodic_noisemap_store</a> (self, interval_minutes, reserved)</td></tr>
<tr class="memdesc:a07a6814f052efd83fe3d61ce96448375"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_periodic_noisemap_store(PyX4M300 self, uint32_t interval_minutes, uint32_t reserved)  <a href="#a07a6814f052efd83fe3d61ce96448375">More...</a><br /></td></tr>
<tr class="separator:a07a6814f052efd83fe3d61ce96448375"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5b4620d151598f1b65c7c7e7d092731"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ad5b4620d151598f1b65c7c7e7d092731">get_periodic_noisemap_store</a> (self)</td></tr>
<tr class="memdesc:ad5b4620d151598f1b65c7c7e7d092731"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_periodic_noisemap_store(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" title="Representation of periodic noisemap store parameters. ">PeriodicNoisemapStore</a>  <a href="#ad5b4620d151598f1b65c7c7e7d092731">More...</a><br /></td></tr>
<tr class="separator:ad5b4620d151598f1b65c7c7e7d092731"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fe857eb3b74576bc60b7756b90aa2c4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2fe857eb3b74576bc60b7756b90aa2c4">get_parameter_file</a> (self, filename)</td></tr>
<tr class="memdesc:a2fe857eb3b74576bc60b7756b90aa2c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_parameter_file(PyX4M300 self, std::string const &amp; filename) -&gt; std::string  <a href="#a2fe857eb3b74576bc60b7756b90aa2c4">More...</a><br /></td></tr>
<tr class="separator:a2fe857eb3b74576bc60b7756b90aa2c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d3f5832a1594e274d5efc26a47011b6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a0d3f5832a1594e274d5efc26a47011b6">get_profileid</a> (self)</td></tr>
<tr class="memdesc:a0d3f5832a1594e274d5efc26a47011b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_profileid(PyX4M300 self) -&gt; uint32_t  <a href="#a0d3f5832a1594e274d5efc26a47011b6">More...</a><br /></td></tr>
<tr class="separator:a0d3f5832a1594e274d5efc26a47011b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada49c26885531b7f3965c4c006c6a804"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ada49c26885531b7f3965c4c006c6a804">set_parameter_file</a> (self, filename, data)</td></tr>
<tr class="memdesc:ada49c26885531b7f3965c4c006c6a804"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_parameter_file(PyX4M300 self, std::string const &amp; filename, std::string const &amp; data)  <a href="#ada49c26885531b7f3965c4c006c6a804">More...</a><br /></td></tr>
<tr class="separator:ada49c26885531b7f3965c4c006c6a804"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6367c4c3284b0ba6a8dfe2ebba4841d1"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a6367c4c3284b0ba6a8dfe2ebba4841d1">set_iopin_control</a> (self, pin_id, pin_setup, pin_feature)</td></tr>
<tr class="memdesc:a6367c4c3284b0ba6a8dfe2ebba4841d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_control(PyX4M300 self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature)  <a href="#a6367c4c3284b0ba6a8dfe2ebba4841d1">More...</a><br /></td></tr>
<tr class="separator:a6367c4c3284b0ba6a8dfe2ebba4841d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8144aa7719f885017b82f9f7540fdd01"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a8144aa7719f885017b82f9f7540fdd01">get_iopin_control</a> (self, pin_id)</td></tr>
<tr class="memdesc:a8144aa7719f885017b82f9f7540fdd01"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_control(PyX4M300 self, uint32_t pin_id) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" title="Representation of io pin control configuration. ">IoPinControl</a>  <a href="#a8144aa7719f885017b82f9f7540fdd01">More...</a><br /></td></tr>
<tr class="separator:a8144aa7719f885017b82f9f7540fdd01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bdec90b34c48f9d253461e4eadf3255"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a0bdec90b34c48f9d253461e4eadf3255">set_iopin_value</a> (self, pin_id, pin_value)</td></tr>
<tr class="memdesc:a0bdec90b34c48f9d253461e4eadf3255"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_value(PyX4M300 self, uint32_t pin_id, uint32_t pin_value)  <a href="#a0bdec90b34c48f9d253461e4eadf3255">More...</a><br /></td></tr>
<tr class="separator:a0bdec90b34c48f9d253461e4eadf3255"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b5e91df6369e644320d0a0ec2a2e109"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a9b5e91df6369e644320d0a0ec2a2e109">get_iopin_value</a> (self, pin_id)</td></tr>
<tr class="memdesc:a9b5e91df6369e644320d0a0ec2a2e109"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_value(PyX4M300 self, uint32_t pin_id) -&gt; uint32_t  <a href="#a9b5e91df6369e644320d0a0ec2a2e109">More...</a><br /></td></tr>
<tr class="separator:a9b5e91df6369e644320d0a0ec2a2e109"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a65497d17f6bcfa798d99b42335ec6a65"><td class="memItemLeft" align="right" valign="top"><a id="a65497d17f6bcfa798d99b42335ec6a65"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a65497d17f6bcfa798d99b42335ec6a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>C++ includes: PyX4M300.hpp. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a873eeee2d3c2d5485ba82447a96c62cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a873eeee2d3c2d5485ba82447a96c62cf">&sect;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>radar_interface</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p><b>init</b>(XeThru::PyX4M300 self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml" title="C++ includes: PyX4M300.hpp. ">PyX4M300</a> </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a078e8eb5598cc7c38b7822f07bd5f989"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a078e8eb5598cc7c38b7822f07bd5f989">&sect;&nbsp;</a></span>delete_noisemap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.delete_noisemap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>delete_noisemap(PyX4M300 self) </p>
<p>Send command to module to delete stored noisemap from module flash.</p>
<p>Fails in case of flash access issues. </p>

</div>
</div>
<a id="a5b4a0068b83623251b89cdfefe508e08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5b4a0068b83623251b89cdfefe508e08">&sect;&nbsp;</a></span>get_debug_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_debug_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_debug_output_control(PyX4M300 self, uint32_t const output_feature) -&gt; uint32_t </p>

</div>
</div>
<a id="aa9bb9b53f33af8a80d06d353893ca181"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9bb9b53f33af8a80d06d353893ca181">&sect;&nbsp;</a></span>get_detection_zone()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_detection_zone </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_detection_zone(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a> </p>
<p>Returns the actual range window.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a> </p>

</div>
</div>
<a id="acd9819f3280ad70c955acfdf0e04504a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd9819f3280ad70c955acfdf0e04504a">&sect;&nbsp;</a></span>get_detection_zone_limits()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_detection_zone_limits </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_detection_zone_limits(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a> </p>
<p>Returns the potential settings of detection zone from the module.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a> </p>

</div>
</div>
<a id="a8144aa7719f885017b82f9f7540fdd01"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8144aa7719f885017b82f9f7540fdd01">&sect;&nbsp;</a></span>get_iopin_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_iopin_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_iopin_control(PyX4M300 self, uint32_t pin_id) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" title="Representation of io pin control configuration. ">IoPinControl</a> </p>
<p>Gets the GPIO pin configuration.</p>
<h2>Parameters </h2>
<ul>
<li><code>pin_id</code> : Specifies the io pin to get.</li>
</ul>
<h2>Returns </h2>
<p>: A data object holding the pin configuration</p>
<p>See set_iopin_contron </p>

</div>
</div>
<a id="a9b5e91df6369e644320d0a0ec2a2e109"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b5e91df6369e644320d0a0ec2a2e109">&sect;&nbsp;</a></span>get_iopin_value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_iopin_value </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_iopin_value(PyX4M300 self, uint32_t pin_id) -&gt; uint32_t </p>
<p>Gets GPIO pin value.</p>
<p>See set_iopin_value </p>

</div>
</div>
<a id="a222ce51e298e12567df5fd366cfab713"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a222ce51e298e12567df5fd366cfab713">&sect;&nbsp;</a></span>get_led_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_led_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_led_control(PyX4M300 self) -&gt; uint32_t </p>
<p>Gets LED mode mode = XTID_LED_MODE_OFF : OFF mode = XTID_LED_MODE_SIMPLE : simple mode = XTID_LED_MODE_FULL : full (default) *.</p>
<h2>Returns </h2>
<p>mode </p>

</div>
</div>
<a id="add9b5c1f49d7a2683f63ba8ed73c9329"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add9b5c1f49d7a2683f63ba8ed73c9329">&sect;&nbsp;</a></span>get_noisemap_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_noisemap_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_noisemap_control(PyX4M300 self) -&gt; uint32_t </p>
<p>Get current noisemap configuration.</p>
<h2>Returns </h2>
<p>noisemap_control A bitfield of the various features. </p>

</div>
</div>
<a id="aba7010d4b3206095c85180406e3b220b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba7010d4b3206095c85180406e3b220b">&sect;&nbsp;</a></span>get_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_output_control(PyX4M300 self, uint32_t const output_feature) -&gt; uint32_t </p>

</div>
</div>
<a id="a2fe857eb3b74576bc60b7756b90aa2c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2fe857eb3b74576bc60b7756b90aa2c4">&sect;&nbsp;</a></span>get_parameter_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_parameter_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>filename</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_parameter_file(PyX4M300 self, std::string const &amp; filename) -&gt; std::string </p>
<p>Read the complete parameter file from the module.</p>
<h2>Returns </h2>
<p>a string containing the complete paramter file data </p>

</div>
</div>
<a id="ad5b4620d151598f1b65c7c7e7d092731"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad5b4620d151598f1b65c7c7e7d092731">&sect;&nbsp;</a></span>get_periodic_noisemap_store()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_periodic_noisemap_store </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_periodic_noisemap_store(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" title="Representation of periodic noisemap store parameters. ">PeriodicNoisemapStore</a> </p>
<p>Get interval for periodoc storing of noisemap.</p>
<h2>Parameters </h2>
<ul>
<li><code>interval_minutes</code> : Interval for storing moisemap</li>
<li><code>reserved</code> : Reserved for future use, must be set to 0. </li>
</ul>

</div>
</div>
<a id="a0d3f5832a1594e274d5efc26a47011b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d3f5832a1594e274d5efc26a47011b6">&sect;&nbsp;</a></span>get_profileid()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_profileid </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_profileid(PyX4M300 self) -&gt; uint32_t </p>
<p>Get the id of the currently loaded profile.</p>
<h2>Returns </h2>
<p>profileid the id of the loaded profile or 0 in case of no loaded profile. </p>

</div>
</div>
<a id="a093c0d01728225c5ebb766d09c279d9c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a093c0d01728225c5ebb766d09c279d9c">&sect;&nbsp;</a></span>get_sensitivity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_sensitivity </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_sensitivity(PyX4M300 self) -&gt; uint32_t </p>
<p>Gets the overall sensitivity.</p>
<h2>Returns </h2>
<p>sensitivity 0 to 9, 0 = low, 9 = high </p>

</div>
</div>
<a id="a660e47c44813d47ca034cda75b5e4d95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a660e47c44813d47ca034cda75b5e4d95">&sect;&nbsp;</a></span>get_sensor_mode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_sensor_mode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_sensor_mode(PyX4M300 self) -&gt; uint8_t </p>

</div>
</div>
<a id="a358163cbc1dd10fa30679d7985db4d8e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a358163cbc1dd10fa30679d7985db4d8e">&sect;&nbsp;</a></span>get_system_info()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_system_info </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>info_code</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_system_info(PyX4M300 self, uint8_t const info_code) -&gt; std::string </p>
<p>Returns a string containing system information given by infocode:</p>
<p>XTID_SSIC_ITEMNUMBER = 0x00 -&gt; Returns the internal Novelda PCBA Item Number, including revision. This is programmed in Flash during manufacturing XTID_SSIC_ORDERCODE = 0x01 -&gt; Returns the PCBA / PCBA stack order code. XTID_SSIC_FIRMWAREID = 0x02 -&gt; Returns the installed Firmware ID. As viewed from the "highest" level of the software, "X4M300". XTID_SSIC_VERSION = 0x03 -&gt; Returns the installed Firmware Version. As viewed from the "highest" level of the software. XTID_SSIC_BUILD = 0x04 -&gt; Returns information of the SW Build installed on the device XTID_SSIC_SERIALNUMBER = 0x06 -&gt; Returns the PCBA serial number XTID_SSIC_VERSIONLIST = 0x07 -&gt; Returns ID and version of all components. Calls all components and compound a string. E.g. "X4M300:1.0.0.3;XEP:2.3.4.5;X4C51:1.0.0.0;DSP:1.1.1.1" </p>

</div>
</div>
<a id="ae345e0319492b7b10d70e69b6c5a6b5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae345e0319492b7b10d70e69b6c5a6b5c">&sect;&nbsp;</a></span>get_tx_center_frequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.get_tx_center_frequency </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_tx_center_frequency(PyX4M300 self) -&gt; uint32_t </p>
<p>Gets TX center frequency.</p>
<h2>Returns </h2>
<p>frequency_band 3 for low band, 4 for high band </p>

</div>
</div>
<a id="a11942b3a8832197300bd0fa041a9b941"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11942b3a8832197300bd0fa041a9b941">&sect;&nbsp;</a></span>inject_frame()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.inject_frame </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frame_counter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frame_length</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frame</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>inject_frame(PyX4M300 self, uint32_t frame_counter, uint32_t frame_length, FloatVector frame) </p>
<p>Injects a radar frame.</p>
<h2>Parameters </h2>
<ul>
<li><code>frame_counter</code> : Frame counter of frame.</li>
<li><code>frame_length</code> : Number of bins in the frame.</li>
<li><code>frame</code> : The frame data to inject.</li>
</ul>
<h2>Returns </h2>
<p>execution status </p>

</div>
</div>
<a id="afd06c82f6163c587f6e07dc4f3ee2e8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd06c82f6163c587f6e07dc4f3ee2e8a">&sect;&nbsp;</a></span>load_noisemap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.load_noisemap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>load_noisemap(PyX4M300 self) </p>
<p>Send command to module to load a previously stored noisemap.</p>
<p>Not yet functional as of FW 1.3 </p>

</div>
</div>
<a id="a745a53c0deebad64c6c2d7d6ea9fc0bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a745a53c0deebad64c6c2d7d6ea9fc0bb">&sect;&nbsp;</a></span>load_profile()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.load_profile </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>profileid</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>load_profile(PyX4M300 self, uint32_t const profileid) </p>
<p>Loads the presence profile.</p>
<p>If another profile is loaded, the other profile is unloaded before the new profile is loaded. The profile does not start, the module remains idle.</p>
<h2>Parameters </h2>
<ul>
<li><code>profileid</code> : the id of the profile to load profileid = XTS_ID_APP_PRESENCE_2 : presence profile </li>
</ul>

</div>
</div>
<a id="ae708c7b6f659138aebca25cab6dd324f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae708c7b6f659138aebca25cab6dd324f">&sect;&nbsp;</a></span>module_reset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.module_reset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>module_reset(PyX4M300 self) </p>
<p>Resets and restart the module.</p>
<p>The client must perform a close and then an open on the ModuleConnector to reeastablish connection. </p>

</div>
</div>
<a id="a27b4ecdc8039088f0cd8e97ce26296f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27b4ecdc8039088f0cd8e97ce26296f6">&sect;&nbsp;</a></span>peek_message_baseband_ap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_baseband_ap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_baseband_ap(PyX4M300 self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="a100dc5ca302556fe28878a50747118ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a100dc5ca302556fe28878a50747118ac">&sect;&nbsp;</a></span>peek_message_baseband_iq()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_baseband_iq </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_baseband_iq(PyX4M300 self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="adbeba432b3601d3740dbee40f97bb603"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbeba432b3601d3740dbee40f97bb603">&sect;&nbsp;</a></span>peek_message_noisemap_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_noisemap_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_noisemap_byte(PyX4M300 self) -&gt; int </p>
<p>Return number of noisemap byte packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="a946c49a6da188672d2eb757895f1eb0f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a946c49a6da188672d2eb757895f1eb0f">&sect;&nbsp;</a></span>peek_message_noisemap_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_noisemap_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_noisemap_float(PyX4M300 self) -&gt; int </p>
<p>Return number of noisemap float packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="a47634fb51ba9c6574478bcbb8a3bb7f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a47634fb51ba9c6574478bcbb8a3bb7f7">&sect;&nbsp;</a></span>peek_message_presence_movinglist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_presence_movinglist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_presence_movinglist(PyX4M300 self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="ac4031dfc7c29c40615a2f947752e9a6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4031dfc7c29c40615a2f947752e9a6e">&sect;&nbsp;</a></span>peek_message_presence_single()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_presence_single </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_presence_single(PyX4M300 self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="a56d0a552e1170fffeea418c4834860b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a56d0a552e1170fffeea418c4834860b2">&sect;&nbsp;</a></span>peek_message_pulsedoppler_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_pulsedoppler_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_pulsedoppler_byte(PyX4M300 self) -&gt; int </p>
<p>Return number of pulse-Doppler byte packets available in the queue.</p>
<h2>Returns </h2>
<p>size number og messages in queue </p>

</div>
</div>
<a id="a28779e4c60bea75906484477d72828f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28779e4c60bea75906484477d72828f5">&sect;&nbsp;</a></span>peek_message_pulsedoppler_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.peek_message_pulsedoppler_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_pulsedoppler_float(PyX4M300 self) -&gt; int </p>
<p>Return number of pulse-Doppler float packets available in the queue.</p>
<h2>Returns </h2>
<p>size number og messages in queue </p>

</div>
</div>
<a id="ab4a434fb77c7b722316a7dfe9cb10f55"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4a434fb77c7b722316a7dfe9cb10f55">&sect;&nbsp;</a></span>ping()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.ping </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>ping(PyX4M300 self) -&gt; uint32_t </p>
<p>Make sure there is a connection to FW on the Xethru X4M200 module.</p>
<h2>Returns </h2>
<p>pong 0xaaeeaeea means system ready and 0xaeeaeeaa means system not ready </p>

</div>
</div>
<a id="ae1981310bed01af00c2a94b438100cd7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae1981310bed01af00c2a94b438100cd7">&sect;&nbsp;</a></span>prepare_inject_frame()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.prepare_inject_frame </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>num_frames</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>num_bins</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>prepare_inject_frame(PyX4M300 self, uint32_t num_frames, uint32_t num_bins, uint32_t mode) </p>
<p>Prepare for injection of radar frame(s).</p>
<h2>Parameters </h2>
<ul>
<li><code>num_frame</code> : Number of frame to inject</li>
<li><code>num_bins</code> : Number of bins in each frame.</li>
<li><code>mode</code> : The frame injection mode: LOOP, SEQUENTIAL, SINGLE</li>
</ul>
<h2>Returns </h2>
<p>execution status </p>

</div>
</div>
<a id="aeb74a781f50c83a6c79ee9647c6f37a2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb74a781f50c83a6c79ee9647c6f37a2">&sect;&nbsp;</a></span>read_message_baseband_ap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_baseband_ap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_baseband_ap(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p>baseband_ap </p>

</div>
</div>
<a id="a37a8e78bd1339fe62929623df1870012"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a37a8e78bd1339fe62929623df1870012">&sect;&nbsp;</a></span>read_message_baseband_iq()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_baseband_iq </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_baseband_iq(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p>baseband_qi </p>

</div>
</div>
<a id="a165b9c2579ddf56c6e61071e661e19e1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a165b9c2579ddf56c6e61071e661e19e1">&sect;&nbsp;</a></span>read_message_noisemap_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_noisemap_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_noisemap_byte(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a> </p>
<p>Get one noisemap byte data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting noisemap byte data </p>

</div>
</div>
<a id="a393fb80799ba2a37d5904d64c874bc39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a393fb80799ba2a37d5904d64c874bc39">&sect;&nbsp;</a></span>read_message_noisemap_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_noisemap_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_noisemap_float(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a> </p>
<p>Get one noisemap float data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting noisemap float data </p>

</div>
</div>
<a id="a4285cf9d408eaca6ee5d67b3e0d33dcc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4285cf9d408eaca6ee5d67b3e0d33dcc">&sect;&nbsp;</a></span>read_message_presence_movinglist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_presence_movinglist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_presence_movinglist(PyX4M300 self) -&gt; PresenceMovingListData </p>
<p>Read a single PresenceMovingList item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p>presence_moving_list </p>

</div>
</div>
<a id="aaa2ada346650ef99eb0fe9ea96d690e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa2ada346650ef99eb0fe9ea96d690e3">&sect;&nbsp;</a></span>read_message_presence_single()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_presence_single </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_presence_single(PyX4M300 self) -&gt; PresenceSingleData </p>
<p>Read a single PresenceSingleData item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p>presence_single </p>

</div>
</div>
<a id="a8df10ebe802cdd6e025148c0a4000f7d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8df10ebe802cdd6e025148c0a4000f7d">&sect;&nbsp;</a></span>read_message_pulsedoppler_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_pulsedoppler_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_pulsedoppler_byte(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a> </p>
<p>Get one pulse-Doppler byte data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting pulse-Doppler float data </p>

</div>
</div>
<a id="a7f4644b3a1d1832dea72d8b1aedb8372"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f4644b3a1d1832dea72d8b1aedb8372">&sect;&nbsp;</a></span>read_message_pulsedoppler_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.read_message_pulsedoppler_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_pulsedoppler_float(PyX4M300 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a> </p>
<p>Get one pulse-Doppler float data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting pulse-Doppler float data </p>

</div>
</div>
<a id="a2209379a8d5e0c4d24032b8d1a1ea5fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2209379a8d5e0c4d24032b8d1a1ea5fd">&sect;&nbsp;</a></span>reset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.reset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>reset(PyX4M300 self) -&gt; int </p>
<p>Resets and restart the module.</p>
<p>This method automatically reestablishes. </p>

</div>
</div>
<a id="ac26286f535f1d4a628ceec852b8a551f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac26286f535f1d4a628ceec852b8a551f">&sect;&nbsp;</a></span>reset_to_factory_preset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.reset_to_factory_preset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>reset_to_factory_preset(PyX4M300 self) </p>
<p>Resets all parameters in the module to factory presets. </p>

</div>
</div>
<a id="a2bee25ec205a5bf0eb06f356a092cc03"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2bee25ec205a5bf0eb06f356a092cc03">&sect;&nbsp;</a></span>set_baudrate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_baudrate </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>baudrate</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_baudrate(PyX4M300 self, uint32_t baudrate) </p>
<p>Set baudrate for serial communication during ModuleConnector operation.</p>
<h2>Parameters </h2>
<ul>
<li><code>baudrate</code> : enum representing the baudrate e.g moduleconnectorwrapper.XTID_BAUDRATE_115200. </li>
</ul>

</div>
</div>
<a id="a085ffc4ab0640ab87c848059467d003f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a085ffc4ab0640ab87c848059467d003f">&sect;&nbsp;</a></span>set_debug_level()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_debug_level </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>level</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_debug_level(PyX4M300 self, unsigned char level) </p>
<p>Sets debug level in the Xethru module.</p>
<h2>Parameters </h2>
<ul>
<li><code>level</code> : New debug level. Legal range [0-9]. </li>
</ul>

</div>
</div>
<a id="a45b0182d2b879249eb564ea193b2d82e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a45b0182d2b879249eb564ea193b2d82e">&sect;&nbsp;</a></span>set_debug_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_debug_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_control</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_debug_output_control(PyX4M300 self, uint32_t output_feature, uint32_t output_control) </p>

</div>
</div>
<a id="adbf23c82bc1db62ea2960e16a1355040"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbf23c82bc1db62ea2960e16a1355040">&sect;&nbsp;</a></span>set_detection_zone()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_detection_zone </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>start</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>end</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_detection_zone(PyX4M300 self, float const start, float const end) </p>
<p>Sets the current detection zone.</p>
<p>Rules See datasheet. The actual detection zone is determined by radar settings. Use the get_detection_zone command to get the actual values</p>
<h2>Parameters </h2>
<ul>
<li><code>start</code> :</li>
<li><code>end</code> : </li>
</ul>

</div>
</div>
<a id="a6367c4c3284b0ba6a8dfe2ebba4841d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6367c4c3284b0ba6a8dfe2ebba4841d1">&sect;&nbsp;</a></span>set_iopin_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_iopin_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_setup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_iopin_control(PyX4M300 self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature) </p>
<p>Configures GPIO pin.</p>
<h2>Parameters </h2>
<ul>
<li><code>pin_id</code> : Specifies the IO pin to configure. pin_id = 0 means all IO pins.</li>
<li><code>pin_setup</code> : Specifies the direction and drive of the IO pin. bit 0: input = 0, output = 1 bit 1: open-drain = 0, push-pull = 1 bit 2: active-high = 0, active-low = 1 bit 3: no pull-up = 0, pull-up = 1</li>
<li><code>pin_feature</code> : Specifies the configuration of the IO pin. 0 = Disable all iopin features. 1 = Configure according to datasheet default. This overrides pin_setup. 2 = Passive, set and get iopin level from host. 3 = Presence, see X4M300 datasheet for details.</li>
</ul>
<p>See get_iopin_value </p>

</div>
</div>
<a id="a0bdec90b34c48f9d253461e4eadf3255"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bdec90b34c48f9d253461e4eadf3255">&sect;&nbsp;</a></span>set_iopin_value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_iopin_value </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_iopin_value(PyX4M300 self, uint32_t pin_id, uint32_t pin_value) </p>
<p>Sets GPIO pin value.</p>
<h2>Parameters </h2>
<ul>
<li><code>pin_id</code> : Specifies the pin.</li>
<li><code>pin_value</code> : Specifies the value.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>Pin must be configured as output pin.</dd></dl>
<p>See set_iopin_control </p>

</div>
</div>
<a id="a083234422dfa614a412b9f42b30ac6e5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a083234422dfa614a412b9f42b30ac6e5">&sect;&nbsp;</a></span>set_led_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_led_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>intensity</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_led_control(PyX4M300 self, uint8_t const mode, uint8_t intensity) </p>
<p>This command configures the LED mode.</p>
<pre class="fragment">    Parameters
    ----------
    * `mode` :
        (modes are defined in xtid.h)
         mode = XTID_LED_MODE_OFF : OFF
         mode = XTID_LED_MODE_SIMPLE : simple
         mode = XTID_LED_MODE_FULL : full (default)
    * `intensity` :
        0 to 100, 0=low, 100=high, not implemented yet</pre> 
</div>
</div>
<a id="a58bd7bc23687822d42384078ed77d775"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58bd7bc23687822d42384078ed77d775">&sect;&nbsp;</a></span>set_noisemap_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_noisemap_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>noisemap_control</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_noisemap_control(PyX4M300 self, uint32_t noisemap_control) </p>
<p>Configure the use of noisemap.</p>
<h2>Parameters </h2>
<ul>
<li><code>noisemap_control</code> : A bitfield of the various features.<ul>
<li>Use Stored Noise Map<ul>
<li>On - XTID_NOISEMAP_CONTROL_USE_STORED<ul>
<li>If a valid Stored Noise Map exists this will be used at reset or when the Profile is started.</li>
<li>If no valid Stored Noise Map exists and Use Default Noise Map is off, a new Noise Map will be created during Initialization state. The newly created Noise Map will be stored in the sensor when Initialization is done. A noise map created with a different Detection Zone is not valid.</li>
<li>If no valid Stored Noise Map exists and Use Default Noise Map is on, the Default Noise Map will be used at reset or when the Profile is started.</li>
</ul>
</li>
<li>Off - XTID_NOISEMAP_CONTROL_INIT_ON_RESET<ul>
<li>If Use Default Noise Map is off, a new Noise Map will always be created at reset or when the Profile is started. Sensor will be in Initialization state during Noise Map creation. A previously Stored Noise Map in the sensor will be ignored, but not overwritten, when the Profile starts.</li>
<li>If Use Default Noise Map is on, the Default Noise Map will be used at reset or when the Profile is started.</li>
</ul>
</li>
</ul>
</li>
<li>Use Default Noise Map<ul>
<li>On - XTID_NOISEMAP_CONTROL_USE_DEFAULT / DISABLE<ul>
<li>If Use Store Noise Map is on and a valid Stored Noise Map exists, Default Noise Map will not be used.</li>
<li>If Use Stored Noise Map is on and no valid Stored Noise Map exists, the Default Noise Map will be used at reset or when the Profile is started.</li>
<li>If Use Stored Noise Map is off, the Default Noise Map will be used at reset or when the Profile is started.</li>
</ul>
</li>
<li>Off - XTID_NOISEMAP_CONTROL_ENABLE<ul>
<li>The Default Noise Map will not be used.</li>
</ul>
</li>
</ul>
</li>
<li>Adaptive Noise Map<ul>
<li>On - XTID_NOISEMAP_CONTROL_ADAPTIVE<ul>
<li>Enables Noise Map adaptation. Noise Map will still not adapt in certain conditions as described in Firmware Algorithms section below.</li>
</ul>
</li>
<li>Off - XTID_NOISEMAP_CONTROL_NONADAPTIVE<ul>
<li>Disables Noise Map adaptation (not implemented). </li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

</div>
</div>
<a id="afa5f8502b525d703418b815c08c767a4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa5f8502b525d703418b815c08c767a4">&sect;&nbsp;</a></span>set_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_control</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_output_control(PyX4M300 self, uint32_t output_feature, uint32_t output_control) </p>
<p>Control module profile output.</p>
<p>Enable and disable data messages. Several calls can be made, one for each available output message the profile provides.</p>
<p>Only one of XTS_ID_BASEBAND_IQ and XTS_ID_BASEBAND_AMPLITUDE_PHASE can be enabled at a time. Enabling one disables the other. Disabling one, even if it is already disabled, disables the other.</p>
<p>Only one of XTS_ID_PULSEDOPPLER_FLOAT and XTS_ID_PULSEDOPPLER_BYTE can be enabled for each of XTID_OUTPUT_CONTROL_PD_SLOW_ENABLE and XTID_OUTPUT_CONTROL_PD_FAST_ENABLE. Same with XTS_ID_NOISEMAP_FLOAT and XTS_ID_NOISEMAP_BYTE. Turning on a float output automatically disables the byte output, and vice versa.</p>
<h2>Parameters </h2>
<ul>
<li><code>output_feature</code> : see values in xtid.h. Possible features are: XTS_ID_PRESENCE_SINGLE, XTS_ID_PRESENCE_MOVINGLIST, XTS_ID_BASEBAND_IQ, XTS_ID_BASEBAND_AMPLITUDE_PHASE, XTS_ID_PULSEDOPPLER_FLOAT, XTS_ID_PULSEDOPPLER_BYTE, XTS_ID_NOISEMAP_FLOAT and XTS_ID_NOISEMAP_BYTE</li>
<li><code>output_control</code> : see values in xtid.h. Typical XTID_OUTPUT_CONTROL_DISABLE = disable, XTID_OUTPUT_CONTROL_ENABLE = enable. For pulse-Doppler and noisemap byte/float: XTID_OUTPUT_CONTROL_PD_SLOW_ENABLE XTID_OUTPUT_CONTROL_PD_FAST_ENABLE </li>
</ul>

</div>
</div>
<a id="ada49c26885531b7f3965c4c006c6a804"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada49c26885531b7f3965c4c006c6a804">&sect;&nbsp;</a></span>set_parameter_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_parameter_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>filename</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_parameter_file(PyX4M300 self, std::string const &amp; filename, std::string const &amp; data) </p>
<p>Set a named parameter file on target.</p>
<h2>Parameters </h2>
<ul>
<li><code>filename</code> : The name to call the parameter file.</li>
<li><code>data</code> : The content of the parameter-file. </li>
</ul>

</div>
</div>
<a id="a07a6814f052efd83fe3d61ce96448375"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a07a6814f052efd83fe3d61ce96448375">&sect;&nbsp;</a></span>set_periodic_noisemap_store()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_periodic_noisemap_store </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>interval_minutes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>reserved</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_periodic_noisemap_store(PyX4M300 self, uint32_t interval_minutes, uint32_t reserved) </p>
<p>Set interval for periodoc storing of noisemap.</p>
<h2>Parameters </h2>
<ul>
<li><code>interval_minutes</code> : Interval for storing moisemap</li>
<li><code>reserved</code> : Reserved for future use, must be set to 0. </li>
</ul>

</div>
</div>
<a id="afe4daf3e2750c49421e4c0cb193d7eb8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe4daf3e2750c49421e4c0cb193d7eb8">&sect;&nbsp;</a></span>set_sensitivity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_sensitivity </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>sensitivity</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_sensitivity(PyX4M300 self, uint32_t const sensitivity) </p>
<p>Sets the overall sensitivity.</p>
<h2>Parameters </h2>
<ul>
<li><code>sensitivity</code> : : 0 to 9, 0 = low, 9 = high </li>
</ul>

</div>
</div>
<a id="a330df328fb76fd7901e76311131a3863"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a330df328fb76fd7901e76311131a3863">&sect;&nbsp;</a></span>set_sensor_mode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_sensor_mode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>param</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_sensor_mode(PyX4M300 self, uint8_t const mode, uint8_t const param) </p>
<p>Control the execution mode of the sensor.</p>
<h2>Parameters </h2>
<ul>
<li><code>mode</code> :</li>
</ul>
<p>XTID_SM_RUN Start profile execution XTID_SM_IDLE Halts profile execution. Can be resumed by setting mode to Run. XTID_SM_STOP Stops profile execution. Must do load_profile to continue. XTID_SM_MANUAL Routes X4 radar data directly to host rather than to profile execution. Can then interact directly with XEP / X4Driver. Will disrupt profile performance.</p><ul>
<li><code>param</code> : Not used, ignored, can be 0. </li>
</ul>

</div>
</div>
<a id="a30800f99e044d7f87c688706254b1cae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a30800f99e044d7f87c688706254b1cae">&sect;&nbsp;</a></span>set_tx_center_frequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.set_tx_center_frequency </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frequency_band</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_tx_center_frequency(PyX4M300 self, uint32_t const frequency_band) </p>
<p>Sets TX center frequency.</p>
<h2>Parameters </h2>
<ul>
<li><code>frequency_band</code> : : 3 for low band, 4 for high band </li>
</ul>

</div>
</div>
<a id="ae4f26481fe14f8b2d18664d87a4b84b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae4f26481fe14f8b2d18664d87a4b84b3">&sect;&nbsp;</a></span>start_bootloader()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.start_bootloader </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>start_bootloader(PyX4M300 self) </p>
<p>Enters the bootloader for FW upgrades. </p>

</div>
</div>
<a id="a05f0c89662aa753db403fb36f7587a4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a05f0c89662aa753db403fb36f7587a4f">&sect;&nbsp;</a></span>store_noisemap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.store_noisemap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>store_noisemap(PyX4M300 self) </p>
<p>Send command to module to store the current noisemap to module flash.</p>
<p>Fails if a store already is active, for example during the first initialize with XTID_NOISEMAP_CONTROL_INIT_ON_RESET disabled. </p>

</div>
</div>
<a id="a142cc1bff05a309bca9188a647a5cbe7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a142cc1bff05a309bca9188a647a5cbe7">&sect;&nbsp;</a></span>system_run_test()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M300.system_run_test </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>testcode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>system_run_test(PyX4M300 self, uint8_t const testcode, ucVector data) -&gt; int </p>
<p>Runs the different manufacturing tests identified by testcode.</p>
<p>Can return any number of results depending on test_mode. Host must know how to parse test results.</p>
<h2>Parameters </h2>
<ul>
<li><code>testcode</code> :</li>
<li><code>data</code> : data buffer containing the result from a test run. </li>
</ul>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
