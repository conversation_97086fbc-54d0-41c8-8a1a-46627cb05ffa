<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.PyX4M200 Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml">PyX4M200</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyX4M200 Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>C++ includes: PyX4M200.hpp.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2e016acba0f48d4ef0d287f9be3104b3"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a2e016acba0f48d4ef0d287f9be3104b3">__init__</a> (self, radar_interface)</td></tr>
<tr class="memdesc:a2e016acba0f48d4ef0d287f9be3104b3"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::PyX4M200 self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml" title="C++ includes: PyX4M200.hpp. ">PyX4M200</a>  <a href="#a2e016acba0f48d4ef0d287f9be3104b3">More...</a><br /></td></tr>
<tr class="separator:a2e016acba0f48d4ef0d287f9be3104b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af469293f646b75661793c6c672cd746d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#af469293f646b75661793c6c672cd746d">set_baudrate</a> (self, baudrate)</td></tr>
<tr class="memdesc:af469293f646b75661793c6c672cd746d"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_baudrate(PyX4M200 self, uint32_t baudrate)  <a href="#af469293f646b75661793c6c672cd746d">More...</a><br /></td></tr>
<tr class="separator:af469293f646b75661793c6c672cd746d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3523741b12df37e1e98e1ad4ef3ef292"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a3523741b12df37e1e98e1ad4ef3ef292">set_debug_level</a> (self, level)</td></tr>
<tr class="memdesc:a3523741b12df37e1e98e1ad4ef3ef292"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_debug_level(PyX4M200 self, unsigned char level)  <a href="#a3523741b12df37e1e98e1ad4ef3ef292">More...</a><br /></td></tr>
<tr class="separator:a3523741b12df37e1e98e1ad4ef3ef292"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb8de5b8ac826dafccfa47353f4aa715"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#adb8de5b8ac826dafccfa47353f4aa715">ping</a> (self)</td></tr>
<tr class="memdesc:adb8de5b8ac826dafccfa47353f4aa715"><td class="mdescLeft">&#160;</td><td class="mdescRight">ping(PyX4M200 self) -&gt; uint32_t  <a href="#adb8de5b8ac826dafccfa47353f4aa715">More...</a><br /></td></tr>
<tr class="separator:adb8de5b8ac826dafccfa47353f4aa715"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e768e062888035dbd6a81d337e9e075"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a7e768e062888035dbd6a81d337e9e075">get_system_info</a> (self, info_code)</td></tr>
<tr class="memdesc:a7e768e062888035dbd6a81d337e9e075"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_system_info(PyX4M200 self, uint8_t const info_code) -&gt; std::string  <a href="#a7e768e062888035dbd6a81d337e9e075">More...</a><br /></td></tr>
<tr class="separator:a7e768e062888035dbd6a81d337e9e075"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a981531900aeabf22f9b28111b8d3dbc4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a981531900aeabf22f9b28111b8d3dbc4">module_reset</a> (self)</td></tr>
<tr class="memdesc:a981531900aeabf22f9b28111b8d3dbc4"><td class="mdescLeft">&#160;</td><td class="mdescRight">module_reset(PyX4M200 self)  <a href="#a981531900aeabf22f9b28111b8d3dbc4">More...</a><br /></td></tr>
<tr class="separator:a981531900aeabf22f9b28111b8d3dbc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdd6156171a0480f7b859468342fc288"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#afdd6156171a0480f7b859468342fc288">reset</a> (self)</td></tr>
<tr class="memdesc:afdd6156171a0480f7b859468342fc288"><td class="mdescLeft">&#160;</td><td class="mdescRight">reset(PyX4M200 self)  <a href="#afdd6156171a0480f7b859468342fc288">More...</a><br /></td></tr>
<tr class="separator:afdd6156171a0480f7b859468342fc288"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a877fc62b046d3c161124cca8dbd3fe2a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a877fc62b046d3c161124cca8dbd3fe2a">reset_to_factory_preset</a> (self)</td></tr>
<tr class="memdesc:a877fc62b046d3c161124cca8dbd3fe2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">reset_to_factory_preset(PyX4M200 self)  <a href="#a877fc62b046d3c161124cca8dbd3fe2a">More...</a><br /></td></tr>
<tr class="separator:a877fc62b046d3c161124cca8dbd3fe2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e1c23e9cd07d718f61db5c3fc74d3bb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a4e1c23e9cd07d718f61db5c3fc74d3bb">start_bootloader</a> (self)</td></tr>
<tr class="memdesc:a4e1c23e9cd07d718f61db5c3fc74d3bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">start_bootloader(PyX4M200 self)  <a href="#a4e1c23e9cd07d718f61db5c3fc74d3bb">More...</a><br /></td></tr>
<tr class="separator:a4e1c23e9cd07d718f61db5c3fc74d3bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad54c5bb15ab27d23428f57e011174985"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad54c5bb15ab27d23428f57e011174985">inject_frame</a> (self, frame_counter, frame_length, frame)</td></tr>
<tr class="memdesc:ad54c5bb15ab27d23428f57e011174985"><td class="mdescLeft">&#160;</td><td class="mdescRight">inject_frame(PyX4M200 self, uint32_t frame_counter, uint32_t frame_length, FloatVector frame)  <a href="#ad54c5bb15ab27d23428f57e011174985">More...</a><br /></td></tr>
<tr class="separator:ad54c5bb15ab27d23428f57e011174985"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abaf334a938cf619dd2d87ea1094126d7"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#abaf334a938cf619dd2d87ea1094126d7">prepare_inject_frame</a> (self, num_frames, num_bins, mode)</td></tr>
<tr class="memdesc:abaf334a938cf619dd2d87ea1094126d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">prepare_inject_frame(PyX4M200 self, uint32_t num_frames, uint32_t num_bins, uint32_t mode)  <a href="#abaf334a938cf619dd2d87ea1094126d7">More...</a><br /></td></tr>
<tr class="separator:abaf334a938cf619dd2d87ea1094126d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9efe674f8049ed7969eeab892e273c9"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad9efe674f8049ed7969eeab892e273c9">system_run_test</a> (self, testcode, data)</td></tr>
<tr class="memdesc:ad9efe674f8049ed7969eeab892e273c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">system_run_test(PyX4M200 self, uint8_t const testcode, ucVector data)  <a href="#ad9efe674f8049ed7969eeab892e273c9">More...</a><br /></td></tr>
<tr class="separator:ad9efe674f8049ed7969eeab892e273c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4ef5d7510b0a768682e5c315fb1f05e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad4ef5d7510b0a768682e5c315fb1f05e">load_profile</a> (self, profileid)</td></tr>
<tr class="memdesc:ad4ef5d7510b0a768682e5c315fb1f05e"><td class="mdescLeft">&#160;</td><td class="mdescRight">load_profile(PyX4M200 self, uint32_t const profileid)  <a href="#ad4ef5d7510b0a768682e5c315fb1f05e">More...</a><br /></td></tr>
<tr class="separator:ad4ef5d7510b0a768682e5c315fb1f05e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad922bb51f10b3e181b1fa5252d48f443"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad922bb51f10b3e181b1fa5252d48f443">set_sensor_mode</a> (self, mode, param)</td></tr>
<tr class="memdesc:ad922bb51f10b3e181b1fa5252d48f443"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_sensor_mode(PyX4M200 self, uint8_t const mode, uint8_t const param)  <a href="#ad922bb51f10b3e181b1fa5252d48f443">More...</a><br /></td></tr>
<tr class="separator:ad922bb51f10b3e181b1fa5252d48f443"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ab7e51ba68932e8f39ee6e04407a167"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a1ab7e51ba68932e8f39ee6e04407a167">get_sensor_mode</a> (self)</td></tr>
<tr class="memdesc:a1ab7e51ba68932e8f39ee6e04407a167"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_sensor_mode(PyX4M200 self) -&gt; uint8_t  <a href="#a1ab7e51ba68932e8f39ee6e04407a167">More...</a><br /></td></tr>
<tr class="separator:a1ab7e51ba68932e8f39ee6e04407a167"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a54b8c3fc6865d4f57ed5b2e3fb7bb6ae"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a54b8c3fc6865d4f57ed5b2e3fb7bb6ae">set_sensitivity</a> (self, sensitivity)</td></tr>
<tr class="memdesc:a54b8c3fc6865d4f57ed5b2e3fb7bb6ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_sensitivity(PyX4M200 self, uint32_t const sensitivity)  <a href="#a54b8c3fc6865d4f57ed5b2e3fb7bb6ae">More...</a><br /></td></tr>
<tr class="separator:a54b8c3fc6865d4f57ed5b2e3fb7bb6ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad530bc6eba3dc220cc01dfecf512873b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad530bc6eba3dc220cc01dfecf512873b">get_sensitivity</a> (self)</td></tr>
<tr class="memdesc:ad530bc6eba3dc220cc01dfecf512873b"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_sensitivity(PyX4M200 self) -&gt; uint32_t  <a href="#ad530bc6eba3dc220cc01dfecf512873b">More...</a><br /></td></tr>
<tr class="separator:ad530bc6eba3dc220cc01dfecf512873b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac41c28eec9e1eda1b7313087cb69a3c0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ac41c28eec9e1eda1b7313087cb69a3c0">set_tx_center_frequency</a> (self, frequency_band)</td></tr>
<tr class="memdesc:ac41c28eec9e1eda1b7313087cb69a3c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_tx_center_frequency(PyX4M200 self, uint32_t const frequency_band)  <a href="#ac41c28eec9e1eda1b7313087cb69a3c0">More...</a><br /></td></tr>
<tr class="separator:ac41c28eec9e1eda1b7313087cb69a3c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc5c489f6e141f099fde86ff1a393ece"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#abc5c489f6e141f099fde86ff1a393ece">get_tx_center_frequency</a> (self)</td></tr>
<tr class="memdesc:abc5c489f6e141f099fde86ff1a393ece"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_tx_center_frequency(PyX4M200 self) -&gt; uint32_t  <a href="#abc5c489f6e141f099fde86ff1a393ece">More...</a><br /></td></tr>
<tr class="separator:abc5c489f6e141f099fde86ff1a393ece"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a698a48e04a58fb18051a9f3951dd5b28"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a698a48e04a58fb18051a9f3951dd5b28">set_detection_zone</a> (self, start, end)</td></tr>
<tr class="memdesc:a698a48e04a58fb18051a9f3951dd5b28"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_detection_zone(PyX4M200 self, float const start, float const end)  <a href="#a698a48e04a58fb18051a9f3951dd5b28">More...</a><br /></td></tr>
<tr class="separator:a698a48e04a58fb18051a9f3951dd5b28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c4fd9c3c37e04d27cd3202c6a8f10e6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a3c4fd9c3c37e04d27cd3202c6a8f10e6">get_detection_zone</a> (self)</td></tr>
<tr class="memdesc:a3c4fd9c3c37e04d27cd3202c6a8f10e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_detection_zone(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a>  <a href="#a3c4fd9c3c37e04d27cd3202c6a8f10e6">More...</a><br /></td></tr>
<tr class="separator:a3c4fd9c3c37e04d27cd3202c6a8f10e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60d73a9109e3b27d95024751a7ac954a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a60d73a9109e3b27d95024751a7ac954a">get_detection_zone_limits</a> (self)</td></tr>
<tr class="memdesc:a60d73a9109e3b27d95024751a7ac954a"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_detection_zone_limits(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a>  <a href="#a60d73a9109e3b27d95024751a7ac954a">More...</a><br /></td></tr>
<tr class="separator:a60d73a9109e3b27d95024751a7ac954a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e433f3f04d447905790085fa880fc3b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a1e433f3f04d447905790085fa880fc3b">set_led_control</a> (self, mode, intensity)</td></tr>
<tr class="memdesc:a1e433f3f04d447905790085fa880fc3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_led_control(PyX4M200 self, uint8_t const mode, uint8_t intensity)  <a href="#a1e433f3f04d447905790085fa880fc3b">More...</a><br /></td></tr>
<tr class="separator:a1e433f3f04d447905790085fa880fc3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a589b2cafac9301bd9ec10836c875af34"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a589b2cafac9301bd9ec10836c875af34">get_led_control</a> (self)</td></tr>
<tr class="memdesc:a589b2cafac9301bd9ec10836c875af34"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_led_control(PyX4M200 self) -&gt; uint32_t  <a href="#a589b2cafac9301bd9ec10836c875af34">More...</a><br /></td></tr>
<tr class="separator:a589b2cafac9301bd9ec10836c875af34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a936abf20cd3178a12b240d57a99eebb0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a936abf20cd3178a12b240d57a99eebb0">set_output_control</a> (self, output_feature, output_control)</td></tr>
<tr class="memdesc:a936abf20cd3178a12b240d57a99eebb0"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_output_control(PyX4M200 self, uint32_t output_feature, uint32_t output_control)  <a href="#a936abf20cd3178a12b240d57a99eebb0">More...</a><br /></td></tr>
<tr class="separator:a936abf20cd3178a12b240d57a99eebb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a039f80a8eb1df436c985fc524acf9a75"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a039f80a8eb1df436c985fc524acf9a75">set_debug_output_control</a> (self, output_feature, output_control)</td></tr>
<tr class="memdesc:a039f80a8eb1df436c985fc524acf9a75"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_debug_output_control(PyX4M200 self, uint32_t output_feature, uint32_t output_control)  <a href="#a039f80a8eb1df436c985fc524acf9a75">More...</a><br /></td></tr>
<tr class="separator:a039f80a8eb1df436c985fc524acf9a75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c7d54f16fb0c778503371fd2115220d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5c7d54f16fb0c778503371fd2115220d">get_output_control</a> (self, output_feature)</td></tr>
<tr class="memdesc:a5c7d54f16fb0c778503371fd2115220d"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_output_control(PyX4M200 self, uint32_t const output_feature) -&gt; uint32_t  <a href="#a5c7d54f16fb0c778503371fd2115220d">More...</a><br /></td></tr>
<tr class="separator:a5c7d54f16fb0c778503371fd2115220d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6cb2067b80882acd78845bdd03453a3a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6cb2067b80882acd78845bdd03453a3a">get_debug_output_control</a> (self, output_feature)</td></tr>
<tr class="memdesc:a6cb2067b80882acd78845bdd03453a3a"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_debug_output_control(PyX4M200 self, uint32_t const output_feature) -&gt; uint32_t  <a href="#a6cb2067b80882acd78845bdd03453a3a">More...</a><br /></td></tr>
<tr class="separator:a6cb2067b80882acd78845bdd03453a3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa061783d204ff958ec09e6798353eb89"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#aa061783d204ff958ec09e6798353eb89">peek_message_baseband_ap</a> (self)</td></tr>
<tr class="memdesc:aa061783d204ff958ec09e6798353eb89"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_baseband_ap(PyX4M200 self) -&gt; int  <a href="#aa061783d204ff958ec09e6798353eb89">More...</a><br /></td></tr>
<tr class="separator:aa061783d204ff958ec09e6798353eb89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ef5b257e1e3f8414a5f0b4b0039c831"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a8ef5b257e1e3f8414a5f0b4b0039c831">read_message_baseband_ap</a> (self)</td></tr>
<tr class="memdesc:a8ef5b257e1e3f8414a5f0b4b0039c831"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_baseband_ap(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a>  <a href="#a8ef5b257e1e3f8414a5f0b4b0039c831">More...</a><br /></td></tr>
<tr class="separator:a8ef5b257e1e3f8414a5f0b4b0039c831"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d30ea4b3a806d3cd6db0ae790acbfe"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a56d30ea4b3a806d3cd6db0ae790acbfe">peek_message_baseband_iq</a> (self)</td></tr>
<tr class="memdesc:a56d30ea4b3a806d3cd6db0ae790acbfe"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_baseband_iq(PyX4M200 self) -&gt; int  <a href="#a56d30ea4b3a806d3cd6db0ae790acbfe">More...</a><br /></td></tr>
<tr class="separator:a56d30ea4b3a806d3cd6db0ae790acbfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72e2c2faab935a92a000a1e82632a887"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a72e2c2faab935a92a000a1e82632a887">read_message_baseband_iq</a> (self)</td></tr>
<tr class="memdesc:a72e2c2faab935a92a000a1e82632a887"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_baseband_iq(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a>  <a href="#a72e2c2faab935a92a000a1e82632a887">More...</a><br /></td></tr>
<tr class="separator:a72e2c2faab935a92a000a1e82632a887"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a096ebf6cf69e20511a19d0fb2118220d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a096ebf6cf69e20511a19d0fb2118220d">peek_message_respiration_legacy</a> (self)</td></tr>
<tr class="memdesc:a096ebf6cf69e20511a19d0fb2118220d"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_legacy(PyX4M200 self) -&gt; int  <a href="#a096ebf6cf69e20511a19d0fb2118220d">More...</a><br /></td></tr>
<tr class="separator:a096ebf6cf69e20511a19d0fb2118220d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50066a1b9a6b23735d24cefac8a40c57"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a50066a1b9a6b23735d24cefac8a40c57">read_message_respiration_legacy</a> (self)</td></tr>
<tr class="memdesc:a50066a1b9a6b23735d24cefac8a40c57"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_legacy(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. ">RespirationData</a>  <a href="#a50066a1b9a6b23735d24cefac8a40c57">More...</a><br /></td></tr>
<tr class="separator:a50066a1b9a6b23735d24cefac8a40c57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accaed4be00bbc8c2e8b92bebcc70345b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#accaed4be00bbc8c2e8b92bebcc70345b">peek_message_respiration_sleep</a> (self)</td></tr>
<tr class="memdesc:accaed4be00bbc8c2e8b92bebcc70345b"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_sleep(PyX4M200 self) -&gt; int  <a href="#accaed4be00bbc8c2e8b92bebcc70345b">More...</a><br /></td></tr>
<tr class="separator:accaed4be00bbc8c2e8b92bebcc70345b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5edf0b4ef081acc72d29965b429690b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#af5edf0b4ef081acc72d29965b429690b">read_message_respiration_sleep</a> (self)</td></tr>
<tr class="memdesc:af5edf0b4ef081acc72d29965b429690b"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_sleep(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml" title="Represents the sleep status data coming from the module. ">SleepData</a>  <a href="#af5edf0b4ef081acc72d29965b429690b">More...</a><br /></td></tr>
<tr class="separator:af5edf0b4ef081acc72d29965b429690b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a543412adef964bb7c6b446b60a648cb9"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a543412adef964bb7c6b446b60a648cb9">peek_message_respiration_movinglist</a> (self)</td></tr>
<tr class="memdesc:a543412adef964bb7c6b446b60a648cb9"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_movinglist(PyX4M200 self) -&gt; int  <a href="#a543412adef964bb7c6b446b60a648cb9">More...</a><br /></td></tr>
<tr class="separator:a543412adef964bb7c6b446b60a648cb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4293f289aa9e8b951a3e81559e4ce773"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a4293f289aa9e8b951a3e81559e4ce773">read_message_respiration_movinglist</a> (self)</td></tr>
<tr class="memdesc:a4293f289aa9e8b951a3e81559e4ce773"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_movinglist(PyX4M200 self) -&gt; RespirationMovingListData  <a href="#a4293f289aa9e8b951a3e81559e4ce773">More...</a><br /></td></tr>
<tr class="separator:a4293f289aa9e8b951a3e81559e4ce773"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e820ac469b17169d568e9f21fe3d687"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a4e820ac469b17169d568e9f21fe3d687">peek_message_respiration_detectionlist</a> (self)</td></tr>
<tr class="memdesc:a4e820ac469b17169d568e9f21fe3d687"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_detectionlist(PyX4M200 self) -&gt; int  <a href="#a4e820ac469b17169d568e9f21fe3d687">More...</a><br /></td></tr>
<tr class="separator:a4e820ac469b17169d568e9f21fe3d687"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1380086b4778bc5ce05bf7017b5f956"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ab1380086b4778bc5ce05bf7017b5f956">read_message_respiration_detectionlist</a> (self)</td></tr>
<tr class="memdesc:ab1380086b4778bc5ce05bf7017b5f956"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_detectionlist(PyX4M200 self) -&gt; RespirationDetectionListData  <a href="#ab1380086b4778bc5ce05bf7017b5f956">More...</a><br /></td></tr>
<tr class="separator:ab1380086b4778bc5ce05bf7017b5f956"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08900f19c0ece92bfee0fea176d7bf4b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a08900f19c0ece92bfee0fea176d7bf4b">peek_message_respiration_normalizedmovementlist</a> (self)</td></tr>
<tr class="memdesc:a08900f19c0ece92bfee0fea176d7bf4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_normalizedmovementlist(PyX4M200 self) -&gt; int  <a href="#a08900f19c0ece92bfee0fea176d7bf4b">More...</a><br /></td></tr>
<tr class="separator:a08900f19c0ece92bfee0fea176d7bf4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae672abd46253481b38f049b0b4439070"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ae672abd46253481b38f049b0b4439070">read_message_respiration_normalizedmovementlist</a> (self)</td></tr>
<tr class="memdesc:ae672abd46253481b38f049b0b4439070"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_normalizedmovementlist(PyX4M200 self) -&gt; RespirationNormalizedMovementListData  <a href="#ae672abd46253481b38f049b0b4439070">More...</a><br /></td></tr>
<tr class="separator:ae672abd46253481b38f049b0b4439070"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5184d7ef46de725ee6a5bf4b5b17908b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5184d7ef46de725ee6a5bf4b5b17908b">peek_message_vital_signs</a> (self)</td></tr>
<tr class="memdesc:a5184d7ef46de725ee6a5bf4b5b17908b"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_vital_signs(PyX4M200 self) -&gt; int  <a href="#a5184d7ef46de725ee6a5bf4b5b17908b">More...</a><br /></td></tr>
<tr class="separator:a5184d7ef46de725ee6a5bf4b5b17908b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c2798d0c7a05a6b62de05f24a329084"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5c2798d0c7a05a6b62de05f24a329084">read_message_vital_signs</a> (self)</td></tr>
<tr class="memdesc:a5c2798d0c7a05a6b62de05f24a329084"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_vital_signs(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml" title="Various vital signs. ">VitalSignsData</a>  <a href="#a5c2798d0c7a05a6b62de05f24a329084">More...</a><br /></td></tr>
<tr class="separator:a5c2798d0c7a05a6b62de05f24a329084"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa51a56a171daa2e6b53d4172fbc5e331"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#aa51a56a171daa2e6b53d4172fbc5e331">peek_message_pulsedoppler_float</a> (self)</td></tr>
<tr class="memdesc:aa51a56a171daa2e6b53d4172fbc5e331"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_pulsedoppler_float(PyX4M200 self) -&gt; int  <a href="#aa51a56a171daa2e6b53d4172fbc5e331">More...</a><br /></td></tr>
<tr class="separator:aa51a56a171daa2e6b53d4172fbc5e331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0d271447f6439bb9ff520c74d4d9183"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#af0d271447f6439bb9ff520c74d4d9183">read_message_pulsedoppler_float</a> (self)</td></tr>
<tr class="memdesc:af0d271447f6439bb9ff520c74d4d9183"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_pulsedoppler_float(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a>  <a href="#af0d271447f6439bb9ff520c74d4d9183">More...</a><br /></td></tr>
<tr class="separator:af0d271447f6439bb9ff520c74d4d9183"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac97a443f9888d345222e96d051524250"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ac97a443f9888d345222e96d051524250">peek_message_pulsedoppler_byte</a> (self)</td></tr>
<tr class="memdesc:ac97a443f9888d345222e96d051524250"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_pulsedoppler_byte(PyX4M200 self) -&gt; int  <a href="#ac97a443f9888d345222e96d051524250">More...</a><br /></td></tr>
<tr class="separator:ac97a443f9888d345222e96d051524250"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa395ad2d12ad3b63eb8bce94e957cf"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a0aa395ad2d12ad3b63eb8bce94e957cf">read_message_pulsedoppler_byte</a> (self)</td></tr>
<tr class="memdesc:a0aa395ad2d12ad3b63eb8bce94e957cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_pulsedoppler_byte(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a>  <a href="#a0aa395ad2d12ad3b63eb8bce94e957cf">More...</a><br /></td></tr>
<tr class="separator:a0aa395ad2d12ad3b63eb8bce94e957cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2efe96b1d38a99515dc344312d60445"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ad2efe96b1d38a99515dc344312d60445">peek_message_noisemap_float</a> (self)</td></tr>
<tr class="memdesc:ad2efe96b1d38a99515dc344312d60445"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_noisemap_float(PyX4M200 self) -&gt; int  <a href="#ad2efe96b1d38a99515dc344312d60445">More...</a><br /></td></tr>
<tr class="separator:ad2efe96b1d38a99515dc344312d60445"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5441e125cbf16ebba07723bacbd606d4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5441e125cbf16ebba07723bacbd606d4">read_message_noisemap_float</a> (self)</td></tr>
<tr class="memdesc:a5441e125cbf16ebba07723bacbd606d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_noisemap_float(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a>  <a href="#a5441e125cbf16ebba07723bacbd606d4">More...</a><br /></td></tr>
<tr class="separator:a5441e125cbf16ebba07723bacbd606d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dbf2a4d0a455f2e63e772fbba9c3c92"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a2dbf2a4d0a455f2e63e772fbba9c3c92">peek_message_noisemap_byte</a> (self)</td></tr>
<tr class="memdesc:a2dbf2a4d0a455f2e63e772fbba9c3c92"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_noisemap_byte(PyX4M200 self) -&gt; int  <a href="#a2dbf2a4d0a455f2e63e772fbba9c3c92">More...</a><br /></td></tr>
<tr class="separator:a2dbf2a4d0a455f2e63e772fbba9c3c92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a241846da92672ea8553658f0354abef7"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a241846da92672ea8553658f0354abef7">read_message_noisemap_byte</a> (self)</td></tr>
<tr class="memdesc:a241846da92672ea8553658f0354abef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_noisemap_byte(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a>  <a href="#a241846da92672ea8553658f0354abef7">More...</a><br /></td></tr>
<tr class="separator:a241846da92672ea8553658f0354abef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7b6126d9daa45b9abee342e88f4b4cd"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ab7b6126d9daa45b9abee342e88f4b4cd">load_noisemap</a> (self)</td></tr>
<tr class="memdesc:ab7b6126d9daa45b9abee342e88f4b4cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">load_noisemap(PyX4M200 self)  <a href="#ab7b6126d9daa45b9abee342e88f4b4cd">More...</a><br /></td></tr>
<tr class="separator:ab7b6126d9daa45b9abee342e88f4b4cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6bfd2b408a6887039a5024f0f0134b8f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6bfd2b408a6887039a5024f0f0134b8f">store_noisemap</a> (self)</td></tr>
<tr class="memdesc:a6bfd2b408a6887039a5024f0f0134b8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">store_noisemap(PyX4M200 self)  <a href="#a6bfd2b408a6887039a5024f0f0134b8f">More...</a><br /></td></tr>
<tr class="separator:a6bfd2b408a6887039a5024f0f0134b8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44296effaae8773702a1d5b88a19cb9d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a44296effaae8773702a1d5b88a19cb9d">delete_noisemap</a> (self)</td></tr>
<tr class="memdesc:a44296effaae8773702a1d5b88a19cb9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">delete_noisemap(PyX4M200 self)  <a href="#a44296effaae8773702a1d5b88a19cb9d">More...</a><br /></td></tr>
<tr class="separator:a44296effaae8773702a1d5b88a19cb9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ba004b644addb6845b21c8b7b96f4e6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a5ba004b644addb6845b21c8b7b96f4e6">set_noisemap_control</a> (self, noisemap_control)</td></tr>
<tr class="memdesc:a5ba004b644addb6845b21c8b7b96f4e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_noisemap_control(PyX4M200 self, uint32_t noisemap_control)  <a href="#a5ba004b644addb6845b21c8b7b96f4e6">More...</a><br /></td></tr>
<tr class="separator:a5ba004b644addb6845b21c8b7b96f4e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e972f77ffe131a30f890275b465a5a8"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6e972f77ffe131a30f890275b465a5a8">get_noisemap_control</a> (self)</td></tr>
<tr class="memdesc:a6e972f77ffe131a30f890275b465a5a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_noisemap_control(PyX4M200 self) -&gt; uint32_t  <a href="#a6e972f77ffe131a30f890275b465a5a8">More...</a><br /></td></tr>
<tr class="separator:a6e972f77ffe131a30f890275b465a5a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae442c0b14493f6ecbfef1b83cd305dee"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ae442c0b14493f6ecbfef1b83cd305dee">set_periodic_noisemap_store</a> (self, interval_minutes, reserved)</td></tr>
<tr class="memdesc:ae442c0b14493f6ecbfef1b83cd305dee"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_periodic_noisemap_store(PyX4M200 self, uint32_t interval_minutes, uint32_t reserved)  <a href="#ae442c0b14493f6ecbfef1b83cd305dee">More...</a><br /></td></tr>
<tr class="separator:ae442c0b14493f6ecbfef1b83cd305dee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28f320bcaeeafcaae613d033319857ed"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a28f320bcaeeafcaae613d033319857ed">get_periodic_noisemap_store</a> (self)</td></tr>
<tr class="memdesc:a28f320bcaeeafcaae613d033319857ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_periodic_noisemap_store(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" title="Representation of periodic noisemap store parameters. ">PeriodicNoisemapStore</a>  <a href="#a28f320bcaeeafcaae613d033319857ed">More...</a><br /></td></tr>
<tr class="separator:a28f320bcaeeafcaae613d033319857ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15c6b8501d7f08dccc12f1e8b903cacc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a15c6b8501d7f08dccc12f1e8b903cacc">get_parameter_file</a> (self, filename)</td></tr>
<tr class="memdesc:a15c6b8501d7f08dccc12f1e8b903cacc"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_parameter_file(PyX4M200 self, std::string const &amp; filename) -&gt; std::string  <a href="#a15c6b8501d7f08dccc12f1e8b903cacc">More...</a><br /></td></tr>
<tr class="separator:a15c6b8501d7f08dccc12f1e8b903cacc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6462020b272cf4b2843467d024ad4d26"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a6462020b272cf4b2843467d024ad4d26">get_profileid</a> (self)</td></tr>
<tr class="memdesc:a6462020b272cf4b2843467d024ad4d26"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_profileid(PyX4M200 self) -&gt; uint32_t  <a href="#a6462020b272cf4b2843467d024ad4d26">More...</a><br /></td></tr>
<tr class="separator:a6462020b272cf4b2843467d024ad4d26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a150edea748f14e6ffdaebbfd8c6b51bb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a150edea748f14e6ffdaebbfd8c6b51bb">set_parameter_file</a> (self, filename, data)</td></tr>
<tr class="memdesc:a150edea748f14e6ffdaebbfd8c6b51bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_parameter_file(PyX4M200 self, std::string const &amp; filename, std::string const &amp; data)  <a href="#a150edea748f14e6ffdaebbfd8c6b51bb">More...</a><br /></td></tr>
<tr class="separator:a150edea748f14e6ffdaebbfd8c6b51bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa43fb34928200ae7fa011abdf7435a29"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#aa43fb34928200ae7fa011abdf7435a29">set_iopin_control</a> (self, pin_id, pin_setup, pin_feature)</td></tr>
<tr class="memdesc:aa43fb34928200ae7fa011abdf7435a29"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_control(PyX4M200 self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature)  <a href="#aa43fb34928200ae7fa011abdf7435a29">More...</a><br /></td></tr>
<tr class="separator:aa43fb34928200ae7fa011abdf7435a29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cf786b0a023f8c2ce986334029d9d51"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a2cf786b0a023f8c2ce986334029d9d51">get_iopin_control</a> (self, pin_id)</td></tr>
<tr class="memdesc:a2cf786b0a023f8c2ce986334029d9d51"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_control(PyX4M200 self, uint32_t pin_id) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" title="Representation of io pin control configuration. ">IoPinControl</a>  <a href="#a2cf786b0a023f8c2ce986334029d9d51">More...</a><br /></td></tr>
<tr class="separator:a2cf786b0a023f8c2ce986334029d9d51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1f5de2ba4be6492c16575919ac29d0e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#ab1f5de2ba4be6492c16575919ac29d0e">set_iopin_value</a> (self, pin_id, pin_value)</td></tr>
<tr class="memdesc:ab1f5de2ba4be6492c16575919ac29d0e"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_value(PyX4M200 self, uint32_t pin_id, uint32_t pin_value)  <a href="#ab1f5de2ba4be6492c16575919ac29d0e">More...</a><br /></td></tr>
<tr class="separator:ab1f5de2ba4be6492c16575919ac29d0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75cb41ee97156e5ec92ba453b71c5463"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml#a75cb41ee97156e5ec92ba453b71c5463">get_iopin_value</a> (self, pin_id)</td></tr>
<tr class="memdesc:a75cb41ee97156e5ec92ba453b71c5463"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_value(PyX4M200 self, uint32_t pin_id) -&gt; uint32_t  <a href="#a75cb41ee97156e5ec92ba453b71c5463">More...</a><br /></td></tr>
<tr class="separator:a75cb41ee97156e5ec92ba453b71c5463"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ab2261b84cfce6dd981db3c7bcbd5fc9f"><td class="memItemLeft" align="right" valign="top"><a id="ab2261b84cfce6dd981db3c7bcbd5fc9f"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:ab2261b84cfce6dd981db3c7bcbd5fc9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>C++ includes: PyX4M200.hpp. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a2e016acba0f48d4ef0d287f9be3104b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2e016acba0f48d4ef0d287f9be3104b3">&sect;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>radar_interface</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p><b>init</b>(XeThru::PyX4M200 self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m200.xhtml" title="C++ includes: PyX4M200.hpp. ">PyX4M200</a> </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a44296effaae8773702a1d5b88a19cb9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44296effaae8773702a1d5b88a19cb9d">&sect;&nbsp;</a></span>delete_noisemap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.delete_noisemap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>delete_noisemap(PyX4M200 self) </p>
<p>Send command to module to delete stored noisemap from module flash.</p>
<p>Fails in case of flash access issues. </p>

</div>
</div>
<a id="a6cb2067b80882acd78845bdd03453a3a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6cb2067b80882acd78845bdd03453a3a">&sect;&nbsp;</a></span>get_debug_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_debug_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_debug_output_control(PyX4M200 self, uint32_t const output_feature) -&gt; uint32_t </p>

</div>
</div>
<a id="a3c4fd9c3c37e04d27cd3202c6a8f10e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c4fd9c3c37e04d27cd3202c6a8f10e6">&sect;&nbsp;</a></span>get_detection_zone()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_detection_zone </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_detection_zone(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a> </p>
<p>Returns the actual range window.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a> </p>

</div>
</div>
<a id="a60d73a9109e3b27d95024751a7ac954a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a60d73a9109e3b27d95024751a7ac954a">&sect;&nbsp;</a></span>get_detection_zone_limits()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_detection_zone_limits </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_detection_zone_limits(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a> </p>
<p>Returns the potential settings of detection zone from the module.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a> </p>

</div>
</div>
<a id="a2cf786b0a023f8c2ce986334029d9d51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cf786b0a023f8c2ce986334029d9d51">&sect;&nbsp;</a></span>get_iopin_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_iopin_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_iopin_control(PyX4M200 self, uint32_t pin_id) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" title="Representation of io pin control configuration. ">IoPinControl</a> </p>
<p>Gets the GPIO pin configuration.</p>
<h2>Parameters </h2>
<ul>
<li><code>pin_id</code> : Specifies the io pin to get.</li>
</ul>
<h2>Returns </h2>
<p>: A data object holding the pin configuration</p>
<p>See set_iopin_contron </p>

</div>
</div>
<a id="a75cb41ee97156e5ec92ba453b71c5463"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75cb41ee97156e5ec92ba453b71c5463">&sect;&nbsp;</a></span>get_iopin_value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_iopin_value </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_iopin_value(PyX4M200 self, uint32_t pin_id) -&gt; uint32_t </p>
<p>Gets GPIO pin value.</p>
<p>See set_iopin_value </p>

</div>
</div>
<a id="a589b2cafac9301bd9ec10836c875af34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a589b2cafac9301bd9ec10836c875af34">&sect;&nbsp;</a></span>get_led_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_led_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_led_control(PyX4M200 self) -&gt; uint32_t </p>
<p>Gets LED mode mode = XTID_LED_MODE_OFF : OFF mode = XTID_LED_MODE_SIMPLE : simple mode = XTID_LED_MODE_FULL : full (default) *.</p>
<h2>Returns </h2>
<p>mode </p>

</div>
</div>
<a id="a6e972f77ffe131a30f890275b465a5a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e972f77ffe131a30f890275b465a5a8">&sect;&nbsp;</a></span>get_noisemap_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_noisemap_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_noisemap_control(PyX4M200 self) -&gt; uint32_t </p>
<p>Get current noisemap configuration.</p>
<h2>Returns </h2>
<p>noisemap_control A bitfield of the various features. </p>

</div>
</div>
<a id="a5c7d54f16fb0c778503371fd2115220d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c7d54f16fb0c778503371fd2115220d">&sect;&nbsp;</a></span>get_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_output_control(PyX4M200 self, uint32_t const output_feature) -&gt; uint32_t </p>

</div>
</div>
<a id="a15c6b8501d7f08dccc12f1e8b903cacc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a15c6b8501d7f08dccc12f1e8b903cacc">&sect;&nbsp;</a></span>get_parameter_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_parameter_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>filename</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_parameter_file(PyX4M200 self, std::string const &amp; filename) -&gt; std::string </p>
<p>Read the complete parameter file from the module.</p>
<h2>Returns </h2>
<p>a string containing the complete paramter file data </p>

</div>
</div>
<a id="a28f320bcaeeafcaae613d033319857ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28f320bcaeeafcaae613d033319857ed">&sect;&nbsp;</a></span>get_periodic_noisemap_store()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_periodic_noisemap_store </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_periodic_noisemap_store(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" title="Representation of periodic noisemap store parameters. ">PeriodicNoisemapStore</a> </p>
<p>Get interval for periodoc storing of noisemap.</p>
<h2>Parameters </h2>
<ul>
<li><code>interval_minutes</code> : Interval for storing moisemap</li>
<li><code>reserved</code> : Reserved for future use, must be set to 0. </li>
</ul>

</div>
</div>
<a id="a6462020b272cf4b2843467d024ad4d26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6462020b272cf4b2843467d024ad4d26">&sect;&nbsp;</a></span>get_profileid()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_profileid </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_profileid(PyX4M200 self) -&gt; uint32_t </p>
<p>Get the id of the currently loaded profile.</p>
<h2>Returns </h2>
<p>profileid the id of the loaded profile or 0 in case of no loaded profile. </p>

</div>
</div>
<a id="ad530bc6eba3dc220cc01dfecf512873b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad530bc6eba3dc220cc01dfecf512873b">&sect;&nbsp;</a></span>get_sensitivity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_sensitivity </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_sensitivity(PyX4M200 self) -&gt; uint32_t </p>
<p>Gets the overall sensitivity.</p>
<h2>Returns </h2>
<p>sensitivity 0 to 9, 0 = low, 9 = high </p>

</div>
</div>
<a id="a1ab7e51ba68932e8f39ee6e04407a167"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ab7e51ba68932e8f39ee6e04407a167">&sect;&nbsp;</a></span>get_sensor_mode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_sensor_mode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_sensor_mode(PyX4M200 self) -&gt; uint8_t </p>

</div>
</div>
<a id="a7e768e062888035dbd6a81d337e9e075"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e768e062888035dbd6a81d337e9e075">&sect;&nbsp;</a></span>get_system_info()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_system_info </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>info_code</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_system_info(PyX4M200 self, uint8_t const info_code) -&gt; std::string </p>
<p>Returns a string containing system information given by infocode:</p>
<p>XTID_SSIC_ITEMNUMBER = 0x00 -&gt; Returns the internal Novelda PCBA Item Number, including revision. This is programmed in Flash during manufacturing XTID_SSIC_ORDERCODE = 0x01 -&gt; Returns the PCBA / PCBA stack order code. XTID_SSIC_FIRMWAREID = 0x02 -&gt; Returns the installed Firmware ID. As viewed from the "highest" level of the software, "X4M300". XTID_SSIC_VERSION = 0x03 -&gt; Returns the installed Firmware Version. As viewed from the "highest" level of the software. XTID_SSIC_BUILD = 0x04 -&gt; Returns information of the SW Build installed on the device XTID_SSIC_SERIALNUMBER = 0x06 -&gt; Returns the PCBA serial number XTID_SSIC_VERSIONLIST = 0x07 -&gt; Returns ID and version of all components. Calls all components and compound a string. E.g. "X4M300:1.0.0.3;XEP:2.3.4.5;X4C51:1.0.0.0;DSP:1.1.1.1" </p>

</div>
</div>
<a id="abc5c489f6e141f099fde86ff1a393ece"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc5c489f6e141f099fde86ff1a393ece">&sect;&nbsp;</a></span>get_tx_center_frequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.get_tx_center_frequency </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_tx_center_frequency(PyX4M200 self) -&gt; uint32_t </p>
<p>Gets TX center frequency.</p>
<h2>Returns </h2>
<p>frequency_band 3 for low band, 4 for high band </p>

</div>
</div>
<a id="ad54c5bb15ab27d23428f57e011174985"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad54c5bb15ab27d23428f57e011174985">&sect;&nbsp;</a></span>inject_frame()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.inject_frame </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frame_counter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frame_length</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frame</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>inject_frame(PyX4M200 self, uint32_t frame_counter, uint32_t frame_length, FloatVector frame) </p>
<p>Injects a radar frame.</p>
<h2>Parameters </h2>
<ul>
<li><code>frame_counter</code> : Frame counter of frame.</li>
<li><code>frame_length</code> : Number of bins in the frame.</li>
<li><code>frame</code> : The frame data to inject.</li>
</ul>
<h2>Returns </h2>
<p>execution status </p>

</div>
</div>
<a id="ab7b6126d9daa45b9abee342e88f4b4cd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7b6126d9daa45b9abee342e88f4b4cd">&sect;&nbsp;</a></span>load_noisemap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.load_noisemap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>load_noisemap(PyX4M200 self) </p>
<p>Send command to module to load a previously stored noisemap.</p>
<p>Not yet functional as of FW 1.3 </p>

</div>
</div>
<a id="ad4ef5d7510b0a768682e5c315fb1f05e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad4ef5d7510b0a768682e5c315fb1f05e">&sect;&nbsp;</a></span>load_profile()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.load_profile </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>profileid</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>load_profile(PyX4M200 self, uint32_t const profileid) </p>
<p>Loads the presence profile.</p>
<p>If another profile is loaded, the other profile is unloaded before the new profile is loaded. The profile does not start, the module remains idle.</p>
<h2>Parameters </h2>
<ul>
<li><code>profileid</code> : the id of the profile to load profileid = XTS_ID_APP_RESPIRATION_2 : sleep profile </li>
</ul>

</div>
</div>
<a id="a981531900aeabf22f9b28111b8d3dbc4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a981531900aeabf22f9b28111b8d3dbc4">&sect;&nbsp;</a></span>module_reset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.module_reset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>module_reset(PyX4M200 self) </p>
<p>Resets and restart the module.</p>
<p>The client must perform a close and then an open on the ModuleConnector to reeastablish connection. </p>

</div>
</div>
<a id="aa061783d204ff958ec09e6798353eb89"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa061783d204ff958ec09e6798353eb89">&sect;&nbsp;</a></span>peek_message_baseband_ap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_baseband_ap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_baseband_ap(PyX4M200 self) -&gt; int </p>
<p>Return number of <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> messages available in queue.</p>
<h2>Returns </h2>
<p>size number of messages in buffer </p>

</div>
</div>
<a id="a56d30ea4b3a806d3cd6db0ae790acbfe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a56d30ea4b3a806d3cd6db0ae790acbfe">&sect;&nbsp;</a></span>peek_message_baseband_iq()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_baseband_iq </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_baseband_iq(PyX4M200 self) -&gt; int </p>
<p>Return number of <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a> messages available in queue.</p>
<h2>Returns </h2>
<p>size number of messages in buffer </p>

</div>
</div>
<a id="a2dbf2a4d0a455f2e63e772fbba9c3c92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2dbf2a4d0a455f2e63e772fbba9c3c92">&sect;&nbsp;</a></span>peek_message_noisemap_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_noisemap_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_noisemap_byte(PyX4M200 self) -&gt; int </p>
<p>Return number of noisemap byte packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="ad2efe96b1d38a99515dc344312d60445"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad2efe96b1d38a99515dc344312d60445">&sect;&nbsp;</a></span>peek_message_noisemap_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_noisemap_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_noisemap_float(PyX4M200 self) -&gt; int </p>
<p>Return number of noisemap float packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="ac97a443f9888d345222e96d051524250"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac97a443f9888d345222e96d051524250">&sect;&nbsp;</a></span>peek_message_pulsedoppler_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_pulsedoppler_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_pulsedoppler_byte(PyX4M200 self) -&gt; int </p>
<p>Return number of pulse-Doppler byte packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="aa51a56a171daa2e6b53d4172fbc5e331"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa51a56a171daa2e6b53d4172fbc5e331">&sect;&nbsp;</a></span>peek_message_pulsedoppler_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_pulsedoppler_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_pulsedoppler_float(PyX4M200 self) -&gt; int </p>
<p>Return number of pulse-Doppler float packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="a4e820ac469b17169d568e9f21fe3d687"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e820ac469b17169d568e9f21fe3d687">&sect;&nbsp;</a></span>peek_message_respiration_detectionlist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_respiration_detectionlist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_respiration_detectionlist(PyX4M200 self) -&gt; int </p>
<p>Return number of respiration detection list packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="a096ebf6cf69e20511a19d0fb2118220d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a096ebf6cf69e20511a19d0fb2118220d">&sect;&nbsp;</a></span>peek_message_respiration_legacy()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_respiration_legacy </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_respiration_legacy(PyX4M200 self) -&gt; int </p>
<p>Return number of legacy respiration packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in buffer </p>

</div>
</div>
<a id="a543412adef964bb7c6b446b60a648cb9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a543412adef964bb7c6b446b60a648cb9">&sect;&nbsp;</a></span>peek_message_respiration_movinglist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_respiration_movinglist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_respiration_movinglist(PyX4M200 self) -&gt; int </p>
<p>Return number of respiration moving list packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="a08900f19c0ece92bfee0fea176d7bf4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a08900f19c0ece92bfee0fea176d7bf4b">&sect;&nbsp;</a></span>peek_message_respiration_normalizedmovementlist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_respiration_normalizedmovementlist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_respiration_normalizedmovementlist(PyX4M200 self) -&gt; int </p>
<p>Return number of respiration normalized movement list packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="accaed4be00bbc8c2e8b92bebcc70345b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#accaed4be00bbc8c2e8b92bebcc70345b">&sect;&nbsp;</a></span>peek_message_respiration_sleep()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_respiration_sleep </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_respiration_sleep(PyX4M200 self) -&gt; int </p>
<p>Return number of respiration sleep packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="a5184d7ef46de725ee6a5bf4b5b17908b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5184d7ef46de725ee6a5bf4b5b17908b">&sect;&nbsp;</a></span>peek_message_vital_signs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.peek_message_vital_signs </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_vital_signs(PyX4M200 self) -&gt; int </p>
<p>Return number of vital signs packets available in the queue.</p>
<h2>Returns </h2>
<p>size number of messages in queue </p>

</div>
</div>
<a id="adb8de5b8ac826dafccfa47353f4aa715"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb8de5b8ac826dafccfa47353f4aa715">&sect;&nbsp;</a></span>ping()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.ping </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>ping(PyX4M200 self) -&gt; uint32_t </p>
<p>Make sure there is a connection to FW on the Xethru X4M200 module.</p>
<h2>Returns </h2>
<p>pong 0xaaeeaeea means system ready and 0xaeeaeeaa means system not ready </p>

</div>
</div>
<a id="abaf334a938cf619dd2d87ea1094126d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abaf334a938cf619dd2d87ea1094126d7">&sect;&nbsp;</a></span>prepare_inject_frame()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.prepare_inject_frame </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>num_frames</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>num_bins</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>prepare_inject_frame(PyX4M200 self, uint32_t num_frames, uint32_t num_bins, uint32_t mode) </p>
<p>Prepare for injection of radar frame(s).</p>
<h2>Parameters </h2>
<ul>
<li><code>num_frame</code> : Number of frame to inject</li>
<li><code>num_bins</code> : Number of bins in each frame.</li>
<li><code>mode</code> : The frame injection mode: LOOP, SEQUENTIAL, SINGLE</li>
</ul>
<h2>Returns </h2>
<p>execution status </p>

</div>
</div>
<a id="a8ef5b257e1e3f8414a5f0b4b0039c831"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ef5b257e1e3f8414a5f0b4b0039c831">&sect;&nbsp;</a></span>read_message_baseband_ap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_baseband_ap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_baseband_ap(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> </p>

</div>
</div>
<a id="a72e2c2faab935a92a000a1e82632a887"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72e2c2faab935a92a000a1e82632a887">&sect;&nbsp;</a></span>read_message_baseband_iq()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_baseband_iq </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_baseband_iq(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p>baseband_qi </p>

</div>
</div>
<a id="a241846da92672ea8553658f0354abef7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a241846da92672ea8553658f0354abef7">&sect;&nbsp;</a></span>read_message_noisemap_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_noisemap_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_noisemap_byte(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a> </p>
<p>Get one noisemap byte data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting noisemap byte data </p>

</div>
</div>
<a id="a5441e125cbf16ebba07723bacbd606d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5441e125cbf16ebba07723bacbd606d4">&sect;&nbsp;</a></span>read_message_noisemap_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_noisemap_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_noisemap_float(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a> </p>
<p>Get one noisemap float data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting noisemap float data </p>

</div>
</div>
<a id="a0aa395ad2d12ad3b63eb8bce94e957cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0aa395ad2d12ad3b63eb8bce94e957cf">&sect;&nbsp;</a></span>read_message_pulsedoppler_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_pulsedoppler_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_pulsedoppler_byte(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a> </p>
<p>Get one pulse-Doppler byte data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting pulse-Doppler byte data </p>

</div>
</div>
<a id="af0d271447f6439bb9ff520c74d4d9183"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0d271447f6439bb9ff520c74d4d9183">&sect;&nbsp;</a></span>read_message_pulsedoppler_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_pulsedoppler_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_pulsedoppler_float(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a> </p>
<p>Get one pulse-Doppler float data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting pulse-Doppler float data </p>

</div>
</div>
<a id="ab1380086b4778bc5ce05bf7017b5f956"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1380086b4778bc5ce05bf7017b5f956">&sect;&nbsp;</a></span>read_message_respiration_detectionlist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_respiration_detectionlist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_respiration_detectionlist(PyX4M200 self) -&gt; RespirationDetectionListData </p>
<p>Get one respiration detection list data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting respiration detection list data </p>

</div>
</div>
<a id="a50066a1b9a6b23735d24cefac8a40c57"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50066a1b9a6b23735d24cefac8a40c57">&sect;&nbsp;</a></span>read_message_respiration_legacy()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_respiration_legacy </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_respiration_legacy(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. ">RespirationData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. ">RespirationData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<p>return pointer to returned <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. ">RespirationData</a> item </p>

</div>
</div>
<a id="a4293f289aa9e8b951a3e81559e4ce773"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4293f289aa9e8b951a3e81559e4ce773">&sect;&nbsp;</a></span>read_message_respiration_movinglist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_respiration_movinglist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_respiration_movinglist(PyX4M200 self) -&gt; RespirationMovingListData </p>
<p>Get one respiration moving list data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting respiration moving list data </p>

</div>
</div>
<a id="ae672abd46253481b38f049b0b4439070"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae672abd46253481b38f049b0b4439070">&sect;&nbsp;</a></span>read_message_respiration_normalizedmovementlist()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_respiration_normalizedmovementlist </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_respiration_normalizedmovementlist(PyX4M200 self) -&gt; RespirationNormalizedMovementListData </p>
<p>Get one respiration normalized movement list data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting respiration normalized movement list data </p>

</div>
</div>
<a id="af5edf0b4ef081acc72d29965b429690b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5edf0b4ef081acc72d29965b429690b">&sect;&nbsp;</a></span>read_message_respiration_sleep()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_respiration_sleep </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_respiration_sleep(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml" title="Represents the sleep status data coming from the module. ">SleepData</a> </p>
<p>Get one sleep data message from subscription queue.</p>
<h2>Returns </h2>
<p>sleep_data A data object holding the resulting respiration data. </p>

</div>
</div>
<a id="a5c2798d0c7a05a6b62de05f24a329084"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c2798d0c7a05a6b62de05f24a329084">&sect;&nbsp;</a></span>read_message_vital_signs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.read_message_vital_signs </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_vital_signs(PyX4M200 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml" title="Various vital signs. ">VitalSignsData</a> </p>
<p>Get one vital signs data message from subscription queue.</p>
<h2>Returns </h2>
<p>: A data object holding the resulting vital signs data </p>

</div>
</div>
<a id="afdd6156171a0480f7b859468342fc288"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afdd6156171a0480f7b859468342fc288">&sect;&nbsp;</a></span>reset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.reset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>reset(PyX4M200 self) </p>
<p>Resets and restart the module.</p>
<p>This method automatically reestablishes. </p>

</div>
</div>
<a id="a877fc62b046d3c161124cca8dbd3fe2a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a877fc62b046d3c161124cca8dbd3fe2a">&sect;&nbsp;</a></span>reset_to_factory_preset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.reset_to_factory_preset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>reset_to_factory_preset(PyX4M200 self) </p>
<p>Resets all parameters in the module to factory presets. </p>

</div>
</div>
<a id="af469293f646b75661793c6c672cd746d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af469293f646b75661793c6c672cd746d">&sect;&nbsp;</a></span>set_baudrate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_baudrate </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>baudrate</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_baudrate(PyX4M200 self, uint32_t baudrate) </p>
<p>Set baudrate for serial communication during ModuleConnector operation.</p>
<h2>Parameters </h2>
<ul>
<li><code>baudrate</code> : enum representing the baudrate e.g moduleconnectorwrapper.XTID_BAUDRATE_115200. </li>
</ul>

</div>
</div>
<a id="a3523741b12df37e1e98e1ad4ef3ef292"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3523741b12df37e1e98e1ad4ef3ef292">&sect;&nbsp;</a></span>set_debug_level()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_debug_level </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>level</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_debug_level(PyX4M200 self, unsigned char level) </p>
<p>Sets debug level in the Xethru module.</p>
<h2>Parameters </h2>
<ul>
<li><code>level</code> : New debug level. Legal range [0-9]. </li>
</ul>

</div>
</div>
<a id="a039f80a8eb1df436c985fc524acf9a75"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a039f80a8eb1df436c985fc524acf9a75">&sect;&nbsp;</a></span>set_debug_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_debug_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_control</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_debug_output_control(PyX4M200 self, uint32_t output_feature, uint32_t output_control) </p>

</div>
</div>
<a id="a698a48e04a58fb18051a9f3951dd5b28"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a698a48e04a58fb18051a9f3951dd5b28">&sect;&nbsp;</a></span>set_detection_zone()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_detection_zone </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>start</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>end</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_detection_zone(PyX4M200 self, float const start, float const end) </p>
<p>Sets the current detection zone.</p>
<p>Rules See datasheet. The actual detection zone is determined by radar settings. Use the get_detection_zone command to get the actual values</p>
<h2>Parameters </h2>
<ul>
<li><code>start</code> :</li>
<li><code>end</code> : </li>
</ul>

</div>
</div>
<a id="aa43fb34928200ae7fa011abdf7435a29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa43fb34928200ae7fa011abdf7435a29">&sect;&nbsp;</a></span>set_iopin_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_iopin_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_setup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_iopin_control(PyX4M200 self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature) </p>
<p>Configures GPIO pin.</p>
<h2>Parameters </h2>
<ul>
<li><code>pin_id</code> : Specifies the IO pin to configure. pin_id = 0 means all IO pins.</li>
<li><code>pin_setup</code> : Specifies the direction and drive of the IO pin. bit 0: input = 0, output = 1 bit 1: open-drain = 0, push-pull = 1 bit 2: active-high = 0, active-low = 1 bit 3: no pull-up = 0, pull-up = 1</li>
<li><code>pin_feature</code> : Specifies the configuration of the IO pin. 0 = Disable all iopin features. 1 = Configure according to datasheet default. This overrides pin_setup. 2 = Passive, set and get iopin level from host. 4 = Movement, see X4M200 datasheet for details. 5 = Breathing, see X4M200 datasheet for details.</li>
</ul>
<p>See get_iopin_value </p>

</div>
</div>
<a id="ab1f5de2ba4be6492c16575919ac29d0e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1f5de2ba4be6492c16575919ac29d0e">&sect;&nbsp;</a></span>set_iopin_value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_iopin_value </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_iopin_value(PyX4M200 self, uint32_t pin_id, uint32_t pin_value) </p>
<p>Sets GPIO pin value.</p>
<h2>Parameters </h2>
<ul>
<li><code>pin_id</code> : Specifies the pin.</li>
<li><code>pin_value</code> : Specifies the value.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>Pin must be configured as output pin.</dd></dl>
<p>See set_iopin_control </p>

</div>
</div>
<a id="a1e433f3f04d447905790085fa880fc3b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1e433f3f04d447905790085fa880fc3b">&sect;&nbsp;</a></span>set_led_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_led_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>intensity</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_led_control(PyX4M200 self, uint8_t const mode, uint8_t intensity) </p>
<p>This command configures the LED mode.</p>
<pre class="fragment">    Parameters
    ----------
    * `mode` :
        (modes are defined in xtid.h)
         mode = XTID_LED_MODE_OFF : OFF
         mode = XTID_LED_MODE_SIMPLE : simple
         mode = XTID_LED_MODE_FULL : full (default)
    * `intensity` :
        0 to 100, 0=low, 100=high, not implemented yet</pre> 
</div>
</div>
<a id="a5ba004b644addb6845b21c8b7b96f4e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ba004b644addb6845b21c8b7b96f4e6">&sect;&nbsp;</a></span>set_noisemap_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_noisemap_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>noisemap_control</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_noisemap_control(PyX4M200 self, uint32_t noisemap_control) </p>
<p>Configure the use of noisemap.</p>
<h2>Parameters </h2>
<ul>
<li><code>noisemap_control</code> : A bitfield of the various features.<ul>
<li>Use Stored Noise Map<ul>
<li>On - XTID_NOISEMAP_CONTROL_USE_STORED<ul>
<li>If a valid Stored Noise Map exists this will be used at reset or when the Profile is started.</li>
<li>If no valid Stored Noise Map exists and Use Default Noise Map is off, a new Noise Map will be created during Initialization state. The newly created Noise Map will be stored in the sensor when Initialization is done. A noise map created with a different Detection Zone is not valid.</li>
<li>If no valid Stored Noise Map exists and Use Default Noise Map is on, the Default Noise Map will be used at reset or when the Profile is started.</li>
</ul>
</li>
<li>Off - XTID_NOISEMAP_CONTROL_INIT_ON_RESET<ul>
<li>If Use Default Noise Map is off, a new Noise Map will always be created at reset or when the Profile is started. Sensor will be in Initialization state during Noise Map creation. A previously Stored Noise Map in the sensor will be ignored, but not overwritten, when the Profile starts.</li>
<li>If Use Default Noise Map is on, the Default Noise Map will be used at reset or when the Profile is started.</li>
</ul>
</li>
</ul>
</li>
<li>Use Default Noise Map<ul>
<li>On - XTID_NOISEMAP_CONTROL_USE_DEFAULT / DISABLE<ul>
<li>If Use Store Noise Map is on and a valid Stored Noise Map exists, Default Noise Map will not be used.</li>
<li>If Use Stored Noise Map is on and no valid Stored Noise Map exists, the Default Noise Map will be used at reset or when the Profile is started.</li>
<li>If Use Stored Noise Map is off, the Default Noise Map will be used at reset or when the Profile is started.</li>
</ul>
</li>
<li>Off - XTID_NOISEMAP_CONTROL_ENABLE<ul>
<li>The Default Noise Map will not be used.</li>
</ul>
</li>
</ul>
</li>
<li>Adaptive Noise Map<ul>
<li>On - XTID_NOISEMAP_CONTROL_ADAPTIVE<ul>
<li>Enables Noise Map adaptation. Noise Map will still not adapt in certain conditions as described in Firmware Algorithms section below.</li>
</ul>
</li>
<li>Off - XTID_NOISEMAP_CONTROL_NONADAPTIVE<ul>
<li>Disables Noise Map adaptation (not implemented). </li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

</div>
</div>
<a id="a936abf20cd3178a12b240d57a99eebb0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a936abf20cd3178a12b240d57a99eebb0">&sect;&nbsp;</a></span>set_output_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_output_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_feature</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>output_control</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_output_control(PyX4M200 self, uint32_t output_feature, uint32_t output_control) </p>
<p>Control module profile output.</p>
<p>Enable and disable data messages. Several calls can be made, one for each available output message the profile provides.</p>
<p>Only one of XTS_ID_BASEBAND_IQ and XTS_ID_BASEBAND_AMPLITUDE_PHASE can be enabled at a time. Enabling one disables the other. Disabling one, even if it is already disabled, disables the other.</p>
<p>Only one of XTS_ID_PULSEDOPPLER_FLOAT and XTS_ID_PULSEDOPPLER_BYTE can be enabled for each of XTID_OUTPUT_CONTROL_PD_SLOW_ENABLE and XTID_OUTPUT_CONTROL_PD_FAST_ENABLE. Same with XTS_ID_NOISEMAP_FLOAT and XTS_ID_NOISEMAP_BYTE. Turning on a float output automatically disables the byte output, and vice versa.</p>
<h2>Parameters </h2>
<ul>
<li><code>output_feature</code> : see values in xtid.h. Possible features are: XTS_ID_RESPIRATION_MOVINGLIST, XTS_ID_RESPIRATION_DETECTIONLIST, XTS_ID_RESP_STATUS, XTS_ID_RESP_STATUS_EXT, XTS_ID_BASEBAND_IQ, XTS_ID_BASEBAND_AMPLITUDE_PHASE, XTS_ID_PULSEDOPPLER_FLOAT, XTS_ID_PULSEDOPPLER_BYTE, XTS_ID_NOISEMAP_FLOAT and XTS_ID_NOISEMAP_BYTE</li>
<li><code>output_control</code> : see values in xtid.h. Typical XTID_OUTPUT_CONTROL_DISABLE = disable, XTID_OUTPUT_CONTROL_ENABLE = enable. For pulse-Doppler and noisemap byte/float: XTID_OUTPUT_CONTROL_PD_SLOW_ENABLE XTID_OUTPUT_CONTROL_PD_FAST_ENABLE </li>
</ul>

</div>
</div>
<a id="a150edea748f14e6ffdaebbfd8c6b51bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a150edea748f14e6ffdaebbfd8c6b51bb">&sect;&nbsp;</a></span>set_parameter_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_parameter_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>filename</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_parameter_file(PyX4M200 self, std::string const &amp; filename, std::string const &amp; data) </p>
<p>Set a named parameter file on target.</p>
<h2>Parameters </h2>
<ul>
<li><code>filename</code> : The name to call the parameter file.</li>
<li><code>data</code> : The content of the parameter-file. </li>
</ul>

</div>
</div>
<a id="ae442c0b14493f6ecbfef1b83cd305dee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae442c0b14493f6ecbfef1b83cd305dee">&sect;&nbsp;</a></span>set_periodic_noisemap_store()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_periodic_noisemap_store </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>interval_minutes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>reserved</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_periodic_noisemap_store(PyX4M200 self, uint32_t interval_minutes, uint32_t reserved) </p>
<p>Set interval for periodoc storing of noisemap.</p>
<h2>Parameters </h2>
<ul>
<li><code>interval_minutes</code> : Interval for storing moisemap</li>
<li><code>reserved</code> : Reserved for future use, must be set to 0. </li>
</ul>

</div>
</div>
<a id="a54b8c3fc6865d4f57ed5b2e3fb7bb6ae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a54b8c3fc6865d4f57ed5b2e3fb7bb6ae">&sect;&nbsp;</a></span>set_sensitivity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_sensitivity </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>sensitivity</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_sensitivity(PyX4M200 self, uint32_t const sensitivity) </p>
<p>Sets the overall sensitivity.</p>
<h2>Parameters </h2>
<ul>
<li><code>sensitivity</code> : : 0 to 9, 0 = low, 9 = high </li>
</ul>

</div>
</div>
<a id="ad922bb51f10b3e181b1fa5252d48f443"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad922bb51f10b3e181b1fa5252d48f443">&sect;&nbsp;</a></span>set_sensor_mode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_sensor_mode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>param</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_sensor_mode(PyX4M200 self, uint8_t const mode, uint8_t const param) </p>
<p>Control the execution mode of the sensor.</p>
<h2>Parameters </h2>
<ul>
<li><code>mode</code> :</li>
</ul>
<p>XTID_SM_RUN Start profile execution XTID_SM_IDLE Halts profile execution. Can be resumed by setting mode to Run. XTID_SM_STOP Stops profile execution. Must do load_profile to continue. XTID_SM_MANUAL Routes X4 radar data directly to host rather than to profile execution. Can then interact directly with XEP / X4Driver. Will disrupt profile performance.</p><ul>
<li><code>param</code> : Not used, ignored, can be 0. </li>
</ul>

</div>
</div>
<a id="ac41c28eec9e1eda1b7313087cb69a3c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac41c28eec9e1eda1b7313087cb69a3c0">&sect;&nbsp;</a></span>set_tx_center_frequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.set_tx_center_frequency </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>frequency_band</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_tx_center_frequency(PyX4M200 self, uint32_t const frequency_band) </p>
<p>Sets TX center frequency.</p>
<h2>Parameters </h2>
<ul>
<li><code>frequency_band</code> : : 3 for low band, 4 for high band </li>
</ul>

</div>
</div>
<a id="a4e1c23e9cd07d718f61db5c3fc74d3bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e1c23e9cd07d718f61db5c3fc74d3bb">&sect;&nbsp;</a></span>start_bootloader()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.start_bootloader </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>start_bootloader(PyX4M200 self) </p>
<p>Enters the bootloader for FW upgrades. </p>

</div>
</div>
<a id="a6bfd2b408a6887039a5024f0f0134b8f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6bfd2b408a6887039a5024f0f0134b8f">&sect;&nbsp;</a></span>store_noisemap()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.store_noisemap </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>store_noisemap(PyX4M200 self) </p>
<p>Send command to module to store the current noisemap to module flash.</p>
<p>Fails if a store already is active, for example during the first initialize with XTID_NOISEMAP_CONTROL_INIT_ON_RESET disabled. </p>

</div>
</div>
<a id="ad9efe674f8049ed7969eeab892e273c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9efe674f8049ed7969eeab892e273c9">&sect;&nbsp;</a></span>system_run_test()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyX4M200.system_run_test </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>testcode</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>system_run_test(PyX4M200 self, uint8_t const testcode, ucVector data) </p>
<p>Runs the different manufacturing tests identified by testcode.</p>
<p>Can return any number of results depending on test_mode. Host must know how to parse test results.</p>
<h2>Parameters </h2>
<ul>
<li><code>testcode</code> :</li>
<li><code>data</code> : data buffer containing the result from a test run. </li>
</ul>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
