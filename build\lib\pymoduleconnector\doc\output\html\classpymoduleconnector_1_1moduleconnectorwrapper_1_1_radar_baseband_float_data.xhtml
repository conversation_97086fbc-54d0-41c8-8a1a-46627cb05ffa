<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml">RadarBasebandFloatData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:abbdbdcea59e984769d8038cb0415c276"><td class="memItemLeft" align="right" valign="top"><a id="abbdbdcea59e984769d8038cb0415c276"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>__init__</b> (self, args)</td></tr>
<tr class="separator:abbdbdcea59e984769d8038cb0415c276"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7d8dab9a210580b6b6cd36ae2c10a1f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml#ac7d8dab9a210580b6b6cd36ae2c10a1f">get_I</a> (self)</td></tr>
<tr class="memdesc:ac7d8dab9a210580b6b6cd36ae2c10a1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_I(RadarBasebandFloatData self) -&gt; FloatVector  <a href="#ac7d8dab9a210580b6b6cd36ae2c10a1f">More...</a><br /></td></tr>
<tr class="separator:ac7d8dab9a210580b6b6cd36ae2c10a1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91a7a8cf1d9a000dfcf353138c3c40d8"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml#a91a7a8cf1d9a000dfcf353138c3c40d8">get_Q</a> (self)</td></tr>
<tr class="memdesc:a91a7a8cf1d9a000dfcf353138c3c40d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_Q(RadarBasebandFloatData self) -&gt; FloatVector  <a href="#a91a7a8cf1d9a000dfcf353138c3c40d8">More...</a><br /></td></tr>
<tr class="separator:a91a7a8cf1d9a000dfcf353138c3c40d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a9544365837c439aa2eeda66fae4ac49d"><td class="memItemLeft" align="right" valign="top"><a id="a9544365837c439aa2eeda66fae4ac49d"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a9544365837c439aa2eeda66fae4ac49d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a9017fb01731648cb1f3cdc2904db789c"><td class="memItemLeft" align="right" valign="top"><a id="a9017fb01731648cb1f3cdc2904db789c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_frame_counter_get, _moduleconnectorwrapper.RadarBasebandFloatData_frame_counter_set)</td></tr>
<tr class="separator:a9017fb01731648cb1f3cdc2904db789c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57fc9257711e2ef793d102c1945cc42f"><td class="memItemLeft" align="right" valign="top"><a id="a57fc9257711e2ef793d102c1945cc42f"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>num_bins</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_num_bins_get, _moduleconnectorwrapper.RadarBasebandFloatData_num_bins_set)</td></tr>
<tr class="separator:a57fc9257711e2ef793d102c1945cc42f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab8394e19144a2bc5867fea144ffeb294"><td class="memItemLeft" align="right" valign="top"><a id="ab8394e19144a2bc5867fea144ffeb294"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>bin_length</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_bin_length_get, _moduleconnectorwrapper.RadarBasebandFloatData_bin_length_set)</td></tr>
<tr class="separator:ab8394e19144a2bc5867fea144ffeb294"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0001e1242c75ccc4657299b826d76b4"><td class="memItemLeft" align="right" valign="top"><a id="ad0001e1242c75ccc4657299b826d76b4"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sample_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_sample_frequency_get, _moduleconnectorwrapper.RadarBasebandFloatData_sample_frequency_set)</td></tr>
<tr class="separator:ad0001e1242c75ccc4657299b826d76b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67743535014e8fe30da0078c1df467ae"><td class="memItemLeft" align="right" valign="top"><a id="a67743535014e8fe30da0078c1df467ae"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>carrier_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_carrier_frequency_get, _moduleconnectorwrapper.RadarBasebandFloatData_carrier_frequency_set)</td></tr>
<tr class="separator:a67743535014e8fe30da0078c1df467ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b5c2d3df834ea9f83679fb58fce417b"><td class="memItemLeft" align="right" valign="top"><a id="a6b5c2d3df834ea9f83679fb58fce417b"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frames_per_second</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_frames_per_second_get, _moduleconnectorwrapper.RadarBasebandFloatData_frames_per_second_set)</td></tr>
<tr class="separator:a6b5c2d3df834ea9f83679fb58fce417b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa03e325cb7e0da40d904f24f34bd7f40"><td class="memItemLeft" align="right" valign="top"><a id="aa03e325cb7e0da40d904f24f34bd7f40"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>range_offset</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_range_offset_get, _moduleconnectorwrapper.RadarBasebandFloatData_range_offset_set)</td></tr>
<tr class="separator:aa03e325cb7e0da40d904f24f34bd7f40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac878567b15236053017a2c9683158ed6"><td class="memItemLeft" align="right" valign="top"><a id="ac878567b15236053017a2c9683158ed6"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>decimation_factor</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_decimation_factor_get, _moduleconnectorwrapper.RadarBasebandFloatData_decimation_factor_set)</td></tr>
<tr class="separator:ac878567b15236053017a2c9683158ed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43563f92cb17ced0700290119e7ac215"><td class="memItemLeft" align="right" valign="top"><a id="a43563f92cb17ced0700290119e7ac215"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>correction_bin</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_correction_bin_get, _moduleconnectorwrapper.RadarBasebandFloatData_correction_bin_set)</td></tr>
<tr class="separator:a43563f92cb17ced0700290119e7ac215"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b942c676823e00a6ca631f3253fcfd9"><td class="memItemLeft" align="right" valign="top"><a id="a0b942c676823e00a6ca631f3253fcfd9"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>correction_i</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_correction_i_get, _moduleconnectorwrapper.RadarBasebandFloatData_correction_i_set)</td></tr>
<tr class="separator:a0b942c676823e00a6ca631f3253fcfd9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a0995bec742458a049550fc416099ba"><td class="memItemLeft" align="right" valign="top"><a id="a5a0995bec742458a049550fc416099ba"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>correction_q</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_correction_q_get, _moduleconnectorwrapper.RadarBasebandFloatData_correction_q_set)</td></tr>
<tr class="separator:a5a0995bec742458a049550fc416099ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ff89f64f917618a0eed0af61c6ba582"><td class="memItemLeft" align="right" valign="top"><a id="a4ff89f64f917618a0eed0af61c6ba582"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>i_data</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_i_data_get, _moduleconnectorwrapper.RadarBasebandFloatData_i_data_set)</td></tr>
<tr class="separator:a4ff89f64f917618a0eed0af61c6ba582"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93eae979ff15826c09014b733708971e"><td class="memItemLeft" align="right" valign="top"><a id="a93eae979ff15826c09014b733708971e"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>q_data</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandFloatData_q_data_get, _moduleconnectorwrapper.RadarBasebandFloatData_q_data_set)</td></tr>
<tr class="separator:a93eae979ff15826c09014b733708971e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac7d8dab9a210580b6b6cd36ae2c10a1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7d8dab9a210580b6b6cd36ae2c10a1f">&sect;&nbsp;</a></span>get_I()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData.get_I </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_I(RadarBasebandFloatData self) -&gt; FloatVector </p>
<p>Returns a reference to the in phase vector. </p>

</div>
</div>
<a id="a91a7a8cf1d9a000dfcf353138c3c40d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91a7a8cf1d9a000dfcf353138c3c40d8">&sect;&nbsp;</a></span>get_Q()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RadarBasebandFloatData.get_Q </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_Q(RadarBasebandFloatData self) -&gt; FloatVector </p>
<p>Returns a reference to the in quadrature phase vector. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
