<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.PyX4M210 Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">PyX4M210</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyX4M210 Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Proxy of C++ XeThru::PyX4M210 class.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a91054f29a0a9a0dce20e687ec61ed96d"><td class="memItemLeft" align="right" valign="top"><a id="a91054f29a0a9a0dce20e687ec61ed96d"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a91054f29a0a9a0dce20e687ec61ed96d">__init__</a> (self, radar_interface)</td></tr>
<tr class="memdesc:a91054f29a0a9a0dce20e687ec61ed96d"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::PyX4M210 self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml" title="Proxy of C++ XeThru::PyX4M210 class. ">PyX4M210</a> <br /></td></tr>
<tr class="separator:a91054f29a0a9a0dce20e687ec61ed96d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afab145f09351f9150531e0f9a4bdac99"><td class="memItemLeft" align="right" valign="top"><a id="afab145f09351f9150531e0f9a4bdac99"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afab145f09351f9150531e0f9a4bdac99">set_baudrate</a> (self, baudrate)</td></tr>
<tr class="memdesc:afab145f09351f9150531e0f9a4bdac99"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_baudrate(PyX4M210 self, uint32_t baudrate) <br /></td></tr>
<tr class="separator:afab145f09351f9150531e0f9a4bdac99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50e5afe9d7539e47e650a48b4426081e"><td class="memItemLeft" align="right" valign="top"><a id="a50e5afe9d7539e47e650a48b4426081e"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a50e5afe9d7539e47e650a48b4426081e">set_debug_level</a> (self, level)</td></tr>
<tr class="memdesc:a50e5afe9d7539e47e650a48b4426081e"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_debug_level(PyX4M210 self, unsigned char level) <br /></td></tr>
<tr class="separator:a50e5afe9d7539e47e650a48b4426081e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79e1f79122f1436b4f3dae68134ecdf"><td class="memItemLeft" align="right" valign="top"><a id="aa79e1f79122f1436b4f3dae68134ecdf"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa79e1f79122f1436b4f3dae68134ecdf">ping</a> (self)</td></tr>
<tr class="memdesc:aa79e1f79122f1436b4f3dae68134ecdf"><td class="mdescLeft">&#160;</td><td class="mdescRight">ping(PyX4M210 self) -&gt; uint32_t <br /></td></tr>
<tr class="separator:aa79e1f79122f1436b4f3dae68134ecdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa825a63b84afa70e68616c11283e033a"><td class="memItemLeft" align="right" valign="top"><a id="aa825a63b84afa70e68616c11283e033a"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa825a63b84afa70e68616c11283e033a">get_system_info</a> (self, info_code)</td></tr>
<tr class="memdesc:aa825a63b84afa70e68616c11283e033a"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_system_info(PyX4M210 self, uint8_t const info_code) -&gt; std::string <br /></td></tr>
<tr class="separator:aa825a63b84afa70e68616c11283e033a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab257269e60d50bfcbc4cfe68a852692f"><td class="memItemLeft" align="right" valign="top"><a id="ab257269e60d50bfcbc4cfe68a852692f"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab257269e60d50bfcbc4cfe68a852692f">module_reset</a> (self)</td></tr>
<tr class="memdesc:ab257269e60d50bfcbc4cfe68a852692f"><td class="mdescLeft">&#160;</td><td class="mdescRight">module_reset(PyX4M210 self) <br /></td></tr>
<tr class="separator:ab257269e60d50bfcbc4cfe68a852692f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e55660358cf4864723cdb823a5830cb"><td class="memItemLeft" align="right" valign="top"><a id="a2e55660358cf4864723cdb823a5830cb"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a2e55660358cf4864723cdb823a5830cb">reset</a> (self)</td></tr>
<tr class="memdesc:a2e55660358cf4864723cdb823a5830cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">reset(PyX4M210 self) <br /></td></tr>
<tr class="separator:a2e55660358cf4864723cdb823a5830cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98b89f02120573ecfb151d8474ffe069"><td class="memItemLeft" align="right" valign="top"><a id="a98b89f02120573ecfb151d8474ffe069"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a98b89f02120573ecfb151d8474ffe069">reset_to_factory_preset</a> (self)</td></tr>
<tr class="memdesc:a98b89f02120573ecfb151d8474ffe069"><td class="mdescLeft">&#160;</td><td class="mdescRight">reset_to_factory_preset(PyX4M210 self) <br /></td></tr>
<tr class="separator:a98b89f02120573ecfb151d8474ffe069"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9416a67bca04b001bda671cc194fd58"><td class="memItemLeft" align="right" valign="top"><a id="ae9416a67bca04b001bda671cc194fd58"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae9416a67bca04b001bda671cc194fd58">start_bootloader</a> (self)</td></tr>
<tr class="memdesc:ae9416a67bca04b001bda671cc194fd58"><td class="mdescLeft">&#160;</td><td class="mdescRight">start_bootloader(PyX4M210 self) <br /></td></tr>
<tr class="separator:ae9416a67bca04b001bda671cc194fd58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11a03e24b3a5fcd01cfa62fb7439da78"><td class="memItemLeft" align="right" valign="top"><a id="a11a03e24b3a5fcd01cfa62fb7439da78"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a11a03e24b3a5fcd01cfa62fb7439da78">inject_frame</a> (self, frame_counter, frame_length, frame)</td></tr>
<tr class="memdesc:a11a03e24b3a5fcd01cfa62fb7439da78"><td class="mdescLeft">&#160;</td><td class="mdescRight">inject_frame(PyX4M210 self, uint32_t frame_counter, uint32_t frame_length, FloatVector frame) <br /></td></tr>
<tr class="separator:a11a03e24b3a5fcd01cfa62fb7439da78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adedfafb1eb24933c8a8a9613a43cf571"><td class="memItemLeft" align="right" valign="top"><a id="adedfafb1eb24933c8a8a9613a43cf571"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adedfafb1eb24933c8a8a9613a43cf571">prepare_inject_frame</a> (self, num_frames, num_bins, mode)</td></tr>
<tr class="memdesc:adedfafb1eb24933c8a8a9613a43cf571"><td class="mdescLeft">&#160;</td><td class="mdescRight">prepare_inject_frame(PyX4M210 self, uint32_t num_frames, uint32_t num_bins, uint32_t mode) <br /></td></tr>
<tr class="separator:adedfafb1eb24933c8a8a9613a43cf571"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a473d2273063ea97d15e3c6b25d67968b"><td class="memItemLeft" align="right" valign="top"><a id="a473d2273063ea97d15e3c6b25d67968b"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a473d2273063ea97d15e3c6b25d67968b">system_run_test</a> (self, testcode, data)</td></tr>
<tr class="memdesc:a473d2273063ea97d15e3c6b25d67968b"><td class="mdescLeft">&#160;</td><td class="mdescRight">system_run_test(PyX4M210 self, uint8_t const testcode, ucVector data) <br /></td></tr>
<tr class="separator:a473d2273063ea97d15e3c6b25d67968b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19cf9e9910601154f1f2f5e6e09be5d9"><td class="memItemLeft" align="right" valign="top"><a id="a19cf9e9910601154f1f2f5e6e09be5d9"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a19cf9e9910601154f1f2f5e6e09be5d9">load_profile</a> (self, profileid)</td></tr>
<tr class="memdesc:a19cf9e9910601154f1f2f5e6e09be5d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">load_profile(PyX4M210 self, uint32_t const profileid) <br /></td></tr>
<tr class="separator:a19cf9e9910601154f1f2f5e6e09be5d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbc67a59155d3f0b4e69460e6b8aacc2"><td class="memItemLeft" align="right" valign="top"><a id="afbc67a59155d3f0b4e69460e6b8aacc2"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afbc67a59155d3f0b4e69460e6b8aacc2">set_sensor_mode</a> (self, mode, param)</td></tr>
<tr class="memdesc:afbc67a59155d3f0b4e69460e6b8aacc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_sensor_mode(PyX4M210 self, uint8_t const mode, uint8_t const param) <br /></td></tr>
<tr class="separator:afbc67a59155d3f0b4e69460e6b8aacc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91bb2bce178f87de0ed0a61d97b495c8"><td class="memItemLeft" align="right" valign="top"><a id="a91bb2bce178f87de0ed0a61d97b495c8"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a91bb2bce178f87de0ed0a61d97b495c8">get_sensor_mode</a> (self)</td></tr>
<tr class="memdesc:a91bb2bce178f87de0ed0a61d97b495c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_sensor_mode(PyX4M210 self) -&gt; uint8_t <br /></td></tr>
<tr class="separator:a91bb2bce178f87de0ed0a61d97b495c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeed10f4647052d4754a04600dc8b766f"><td class="memItemLeft" align="right" valign="top"><a id="aeed10f4647052d4754a04600dc8b766f"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aeed10f4647052d4754a04600dc8b766f">set_sensitivity</a> (self, sensitivity)</td></tr>
<tr class="memdesc:aeed10f4647052d4754a04600dc8b766f"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_sensitivity(PyX4M210 self, uint32_t const sensitivity) <br /></td></tr>
<tr class="separator:aeed10f4647052d4754a04600dc8b766f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4ca8d9c0cb9ba5da633a3d57305b487"><td class="memItemLeft" align="right" valign="top"><a id="ac4ca8d9c0cb9ba5da633a3d57305b487"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ac4ca8d9c0cb9ba5da633a3d57305b487">get_sensitivity</a> (self)</td></tr>
<tr class="memdesc:ac4ca8d9c0cb9ba5da633a3d57305b487"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_sensitivity(PyX4M210 self) -&gt; uint32_t <br /></td></tr>
<tr class="separator:ac4ca8d9c0cb9ba5da633a3d57305b487"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9dc109f85098eded5ee441bc72e75329"><td class="memItemLeft" align="right" valign="top"><a id="a9dc109f85098eded5ee441bc72e75329"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a9dc109f85098eded5ee441bc72e75329">set_tx_center_frequency</a> (self, frequency_band)</td></tr>
<tr class="memdesc:a9dc109f85098eded5ee441bc72e75329"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_tx_center_frequency(PyX4M210 self, uint32_t const frequency_band) <br /></td></tr>
<tr class="separator:a9dc109f85098eded5ee441bc72e75329"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32cc1f185eb4f5e26c5c8211b244dbe0"><td class="memItemLeft" align="right" valign="top"><a id="a32cc1f185eb4f5e26c5c8211b244dbe0"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a32cc1f185eb4f5e26c5c8211b244dbe0">get_tx_center_frequency</a> (self)</td></tr>
<tr class="memdesc:a32cc1f185eb4f5e26c5c8211b244dbe0"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_tx_center_frequency(PyX4M210 self) -&gt; uint32_t <br /></td></tr>
<tr class="separator:a32cc1f185eb4f5e26c5c8211b244dbe0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb588a48e37cce16c1d52d1caa4eb424"><td class="memItemLeft" align="right" valign="top"><a id="adb588a48e37cce16c1d52d1caa4eb424"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adb588a48e37cce16c1d52d1caa4eb424">set_detection_zone</a> (self, start, end)</td></tr>
<tr class="memdesc:adb588a48e37cce16c1d52d1caa4eb424"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_detection_zone(PyX4M210 self, float const start, float const end) <br /></td></tr>
<tr class="separator:adb588a48e37cce16c1d52d1caa4eb424"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b4773b7c852d0c4e2c5bac8ab128f83"><td class="memItemLeft" align="right" valign="top"><a id="a8b4773b7c852d0c4e2c5bac8ab128f83"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a8b4773b7c852d0c4e2c5bac8ab128f83">get_detection_zone</a> (self)</td></tr>
<tr class="memdesc:a8b4773b7c852d0c4e2c5bac8ab128f83"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_detection_zone(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone.xhtml" title="Representation of the detection zone. ">DetectionZone</a> <br /></td></tr>
<tr class="separator:a8b4773b7c852d0c4e2c5bac8ab128f83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98ac4c7f8c7d54b3b31edd1abd3c2bda"><td class="memItemLeft" align="right" valign="top"><a id="a98ac4c7f8c7d54b3b31edd1abd3c2bda"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a98ac4c7f8c7d54b3b31edd1abd3c2bda">get_detection_zone_limits</a> (self)</td></tr>
<tr class="memdesc:a98ac4c7f8c7d54b3b31edd1abd3c2bda"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_detection_zone_limits(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_detection_zone_limits.xhtml" title="Is an aggrgation of parameters used to represent the detection zone limits. ">DetectionZoneLimits</a> <br /></td></tr>
<tr class="separator:a98ac4c7f8c7d54b3b31edd1abd3c2bda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5c41bc427410450627c7645e72780ab"><td class="memItemLeft" align="right" valign="top"><a id="ad5c41bc427410450627c7645e72780ab"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad5c41bc427410450627c7645e72780ab">set_led_control</a> (self, mode, intensity)</td></tr>
<tr class="memdesc:ad5c41bc427410450627c7645e72780ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_led_control(PyX4M210 self, uint8_t const mode, uint8_t intensity) <br /></td></tr>
<tr class="separator:ad5c41bc427410450627c7645e72780ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48696644525888d29cbdc919efce04c4"><td class="memItemLeft" align="right" valign="top"><a id="a48696644525888d29cbdc919efce04c4"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a48696644525888d29cbdc919efce04c4">get_led_control</a> (self)</td></tr>
<tr class="memdesc:a48696644525888d29cbdc919efce04c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_led_control(PyX4M210 self) -&gt; uint32_t <br /></td></tr>
<tr class="separator:a48696644525888d29cbdc919efce04c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90875143cc49a39ed93944d61a86a4e5"><td class="memItemLeft" align="right" valign="top"><a id="a90875143cc49a39ed93944d61a86a4e5"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a90875143cc49a39ed93944d61a86a4e5">set_output_control</a> (self, output_feature, output_control)</td></tr>
<tr class="memdesc:a90875143cc49a39ed93944d61a86a4e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_output_control(PyX4M210 self, uint32_t output_feature, uint32_t output_control) <br /></td></tr>
<tr class="separator:a90875143cc49a39ed93944d61a86a4e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02b4d758a3cc6b5a111d55957e6c64a7"><td class="memItemLeft" align="right" valign="top"><a id="a02b4d758a3cc6b5a111d55957e6c64a7"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a02b4d758a3cc6b5a111d55957e6c64a7">set_debug_output_control</a> (self, output_feature, output_control)</td></tr>
<tr class="memdesc:a02b4d758a3cc6b5a111d55957e6c64a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_debug_output_control(PyX4M210 self, uint32_t output_feature, uint32_t output_control) <br /></td></tr>
<tr class="separator:a02b4d758a3cc6b5a111d55957e6c64a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f102c853bf23c0bae7c787d93c8ef7e"><td class="memItemLeft" align="right" valign="top"><a id="a7f102c853bf23c0bae7c787d93c8ef7e"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7f102c853bf23c0bae7c787d93c8ef7e">get_output_control</a> (self, output_feature)</td></tr>
<tr class="memdesc:a7f102c853bf23c0bae7c787d93c8ef7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_output_control(PyX4M210 self, uint32_t const output_feature) -&gt; uint32_t <br /></td></tr>
<tr class="separator:a7f102c853bf23c0bae7c787d93c8ef7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac7e55dbb655536bdacaaf535eab7bd5"><td class="memItemLeft" align="right" valign="top"><a id="aac7e55dbb655536bdacaaf535eab7bd5"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aac7e55dbb655536bdacaaf535eab7bd5">get_debug_output_control</a> (self, output_feature)</td></tr>
<tr class="memdesc:aac7e55dbb655536bdacaaf535eab7bd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_debug_output_control(PyX4M210 self, uint32_t const output_feature) -&gt; uint32_t <br /></td></tr>
<tr class="separator:aac7e55dbb655536bdacaaf535eab7bd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2de0128d8ad9bc933e0854fda0e15eaa"><td class="memItemLeft" align="right" valign="top"><a id="a2de0128d8ad9bc933e0854fda0e15eaa"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a2de0128d8ad9bc933e0854fda0e15eaa">peek_message_baseband_ap</a> (self)</td></tr>
<tr class="memdesc:a2de0128d8ad9bc933e0854fda0e15eaa"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_baseband_ap(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a2de0128d8ad9bc933e0854fda0e15eaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f8cea3e7255ec52ee7444b9ae702c8c"><td class="memItemLeft" align="right" valign="top"><a id="a1f8cea3e7255ec52ee7444b9ae702c8c"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1f8cea3e7255ec52ee7444b9ae702c8c">read_message_baseband_ap</a> (self)</td></tr>
<tr class="memdesc:a1f8cea3e7255ec52ee7444b9ae702c8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_baseband_ap(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_ap_data.xhtml">BasebandApData</a> <br /></td></tr>
<tr class="separator:a1f8cea3e7255ec52ee7444b9ae702c8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8f76e7ec0075b605b969bfef486fe8d"><td class="memItemLeft" align="right" valign="top"><a id="ae8f76e7ec0075b605b969bfef486fe8d"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae8f76e7ec0075b605b969bfef486fe8d">peek_message_baseband_iq</a> (self)</td></tr>
<tr class="memdesc:ae8f76e7ec0075b605b969bfef486fe8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_baseband_iq(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:ae8f76e7ec0075b605b969bfef486fe8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0efb364b393388051d393d7e9853ebc3"><td class="memItemLeft" align="right" valign="top"><a id="a0efb364b393388051d393d7e9853ebc3"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0efb364b393388051d393d7e9853ebc3">read_message_baseband_iq</a> (self)</td></tr>
<tr class="memdesc:a0efb364b393388051d393d7e9853ebc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_baseband_iq(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_baseband_iq_data.xhtml">BasebandIqData</a> <br /></td></tr>
<tr class="separator:a0efb364b393388051d393d7e9853ebc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0eb1092c29c53f6727e5785f3c6ddf90"><td class="memItemLeft" align="right" valign="top"><a id="a0eb1092c29c53f6727e5785f3c6ddf90"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0eb1092c29c53f6727e5785f3c6ddf90">peek_message_respiration_legacy</a> (self)</td></tr>
<tr class="memdesc:a0eb1092c29c53f6727e5785f3c6ddf90"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_legacy(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a0eb1092c29c53f6727e5785f3c6ddf90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38ec0a7a881b4669d69ea38fd5bbff56"><td class="memItemLeft" align="right" valign="top"><a id="a38ec0a7a881b4669d69ea38fd5bbff56"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a38ec0a7a881b4669d69ea38fd5bbff56">read_message_respiration_legacy</a> (self)</td></tr>
<tr class="memdesc:a38ec0a7a881b4669d69ea38fd5bbff56"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_legacy(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_respiration_data.xhtml" title="Represents the respiration status data coming from the module. ">RespirationData</a> <br /></td></tr>
<tr class="separator:a38ec0a7a881b4669d69ea38fd5bbff56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a1d644b5e30c7ddf3707596d61a4b98"><td class="memItemLeft" align="right" valign="top"><a id="a7a1d644b5e30c7ddf3707596d61a4b98"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7a1d644b5e30c7ddf3707596d61a4b98">peek_message_respiration_sleep</a> (self)</td></tr>
<tr class="memdesc:a7a1d644b5e30c7ddf3707596d61a4b98"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_sleep(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a7a1d644b5e30c7ddf3707596d61a4b98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96cad1f147acd0dbf4e4f8e1839d613e"><td class="memItemLeft" align="right" valign="top"><a id="a96cad1f147acd0dbf4e4f8e1839d613e"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a96cad1f147acd0dbf4e4f8e1839d613e">read_message_respiration_sleep</a> (self)</td></tr>
<tr class="memdesc:a96cad1f147acd0dbf4e4f8e1839d613e"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_sleep(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_sleep_data.xhtml" title="Represents the sleep status data coming from the module. ">SleepData</a> <br /></td></tr>
<tr class="separator:a96cad1f147acd0dbf4e4f8e1839d613e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04d9010151df0e51d40a31f145f20960"><td class="memItemLeft" align="right" valign="top"><a id="a04d9010151df0e51d40a31f145f20960"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a04d9010151df0e51d40a31f145f20960">peek_message_respiration_movinglist</a> (self)</td></tr>
<tr class="memdesc:a04d9010151df0e51d40a31f145f20960"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_movinglist(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a04d9010151df0e51d40a31f145f20960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed360a7cf77b630cb1325606d46c5a7c"><td class="memItemLeft" align="right" valign="top"><a id="aed360a7cf77b630cb1325606d46c5a7c"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aed360a7cf77b630cb1325606d46c5a7c">read_message_respiration_movinglist</a> (self)</td></tr>
<tr class="memdesc:aed360a7cf77b630cb1325606d46c5a7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_movinglist(PyX4M210 self) -&gt; RespirationMovingListData <br /></td></tr>
<tr class="separator:aed360a7cf77b630cb1325606d46c5a7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24a099f3f8f899c19ee036ea44b17bd5"><td class="memItemLeft" align="right" valign="top"><a id="a24a099f3f8f899c19ee036ea44b17bd5"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a24a099f3f8f899c19ee036ea44b17bd5">peek_message_respiration_detectionlist</a> (self)</td></tr>
<tr class="memdesc:a24a099f3f8f899c19ee036ea44b17bd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_detectionlist(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a24a099f3f8f899c19ee036ea44b17bd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f37c89243269814e4f6cbe778435548"><td class="memItemLeft" align="right" valign="top"><a id="a1f37c89243269814e4f6cbe778435548"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1f37c89243269814e4f6cbe778435548">read_message_respiration_detectionlist</a> (self)</td></tr>
<tr class="memdesc:a1f37c89243269814e4f6cbe778435548"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_detectionlist(PyX4M210 self) -&gt; RespirationDetectionListData <br /></td></tr>
<tr class="separator:a1f37c89243269814e4f6cbe778435548"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b0835578e0632256ff5503414b93db2"><td class="memItemLeft" align="right" valign="top"><a id="a9b0835578e0632256ff5503414b93db2"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a9b0835578e0632256ff5503414b93db2">peek_message_respiration_normalizedmovementlist</a> (self)</td></tr>
<tr class="memdesc:a9b0835578e0632256ff5503414b93db2"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_respiration_normalizedmovementlist(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a9b0835578e0632256ff5503414b93db2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae99b5753066a2cd2a16b99cf2e640838"><td class="memItemLeft" align="right" valign="top"><a id="ae99b5753066a2cd2a16b99cf2e640838"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae99b5753066a2cd2a16b99cf2e640838">read_message_respiration_normalizedmovementlist</a> (self)</td></tr>
<tr class="memdesc:ae99b5753066a2cd2a16b99cf2e640838"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_respiration_normalizedmovementlist(PyX4M210 self) -&gt; RespirationNormalizedMovementListData <br /></td></tr>
<tr class="separator:ae99b5753066a2cd2a16b99cf2e640838"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5905b42d3f5bc3f71c8d2f267930fa2"><td class="memItemLeft" align="right" valign="top"><a id="aa5905b42d3f5bc3f71c8d2f267930fa2"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa5905b42d3f5bc3f71c8d2f267930fa2">peek_message_vital_signs</a> (self)</td></tr>
<tr class="memdesc:aa5905b42d3f5bc3f71c8d2f267930fa2"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_vital_signs(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:aa5905b42d3f5bc3f71c8d2f267930fa2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7babc3ff1cd64d4fba43358554b4e9f"><td class="memItemLeft" align="right" valign="top"><a id="af7babc3ff1cd64d4fba43358554b4e9f"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#af7babc3ff1cd64d4fba43358554b4e9f">read_message_vital_signs</a> (self)</td></tr>
<tr class="memdesc:af7babc3ff1cd64d4fba43358554b4e9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_vital_signs(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_vital_signs_data.xhtml" title="Various vital signs. ">VitalSignsData</a> <br /></td></tr>
<tr class="separator:af7babc3ff1cd64d4fba43358554b4e9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90814a8cac39204830af91a894b66d5f"><td class="memItemLeft" align="right" valign="top"><a id="a90814a8cac39204830af91a894b66d5f"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a90814a8cac39204830af91a894b66d5f">peek_message_pulsedoppler_float</a> (self)</td></tr>
<tr class="memdesc:a90814a8cac39204830af91a894b66d5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_pulsedoppler_float(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a90814a8cac39204830af91a894b66d5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a475ce19a1fbdc6929a3e35e7af91ae58"><td class="memItemLeft" align="right" valign="top"><a id="a475ce19a1fbdc6929a3e35e7af91ae58"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a475ce19a1fbdc6929a3e35e7af91ae58">read_message_pulsedoppler_float</a> (self)</td></tr>
<tr class="memdesc:a475ce19a1fbdc6929a3e35e7af91ae58"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_pulsedoppler_float(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a> <br /></td></tr>
<tr class="separator:a475ce19a1fbdc6929a3e35e7af91ae58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34daa5adfa85a459da5116edb1451bff"><td class="memItemLeft" align="right" valign="top"><a id="a34daa5adfa85a459da5116edb1451bff"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a34daa5adfa85a459da5116edb1451bff">peek_message_pulsedoppler_byte</a> (self)</td></tr>
<tr class="memdesc:a34daa5adfa85a459da5116edb1451bff"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_pulsedoppler_byte(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a34daa5adfa85a459da5116edb1451bff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81acb200ffc094404d15efc3fd952fb1"><td class="memItemLeft" align="right" valign="top"><a id="a81acb200ffc094404d15efc3fd952fb1"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a81acb200ffc094404d15efc3fd952fb1">read_message_pulsedoppler_byte</a> (self)</td></tr>
<tr class="memdesc:a81acb200ffc094404d15efc3fd952fb1"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_pulsedoppler_byte(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a> <br /></td></tr>
<tr class="separator:a81acb200ffc094404d15efc3fd952fb1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5028e2e04f37122c6979e4a0e1287ecd"><td class="memItemLeft" align="right" valign="top"><a id="a5028e2e04f37122c6979e4a0e1287ecd"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a5028e2e04f37122c6979e4a0e1287ecd">peek_message_noisemap_float</a> (self)</td></tr>
<tr class="memdesc:a5028e2e04f37122c6979e4a0e1287ecd"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_noisemap_float(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:a5028e2e04f37122c6979e4a0e1287ecd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad240dbd66b6513c993ddba2287043d08"><td class="memItemLeft" align="right" valign="top"><a id="ad240dbd66b6513c993ddba2287043d08"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad240dbd66b6513c993ddba2287043d08">read_message_noisemap_float</a> (self)</td></tr>
<tr class="memdesc:ad240dbd66b6513c993ddba2287043d08"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_noisemap_float(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_float_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in float format. ">PulseDopplerFloatData</a> <br /></td></tr>
<tr class="separator:ad240dbd66b6513c993ddba2287043d08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb312b6ecf4684fb126fad0251bb98de"><td class="memItemLeft" align="right" valign="top"><a id="afb312b6ecf4684fb126fad0251bb98de"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afb312b6ecf4684fb126fad0251bb98de">peek_message_noisemap_byte</a> (self)</td></tr>
<tr class="memdesc:afb312b6ecf4684fb126fad0251bb98de"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_noisemap_byte(PyX4M210 self) -&gt; int <br /></td></tr>
<tr class="separator:afb312b6ecf4684fb126fad0251bb98de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59cae5165608a77e836138a76322c56a"><td class="memItemLeft" align="right" valign="top"><a id="a59cae5165608a77e836138a76322c56a"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a59cae5165608a77e836138a76322c56a">read_message_noisemap_byte</a> (self)</td></tr>
<tr class="memdesc:a59cae5165608a77e836138a76322c56a"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_noisemap_byte(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_pulse_doppler_byte_data.xhtml" title="Represents one half or one range bin of pulse-Doppler in byte format. ">PulseDopplerByteData</a> <br /></td></tr>
<tr class="separator:a59cae5165608a77e836138a76322c56a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac738c47adc1eda2da0844d121740402a"><td class="memItemLeft" align="right" valign="top"><a id="ac738c47adc1eda2da0844d121740402a"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ac738c47adc1eda2da0844d121740402a">load_noisemap</a> (self)</td></tr>
<tr class="memdesc:ac738c47adc1eda2da0844d121740402a"><td class="mdescLeft">&#160;</td><td class="mdescRight">load_noisemap(PyX4M210 self) <br /></td></tr>
<tr class="separator:ac738c47adc1eda2da0844d121740402a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0209fabce1f444169d3ebb57c1348e3a"><td class="memItemLeft" align="right" valign="top"><a id="a0209fabce1f444169d3ebb57c1348e3a"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0209fabce1f444169d3ebb57c1348e3a">store_noisemap</a> (self)</td></tr>
<tr class="memdesc:a0209fabce1f444169d3ebb57c1348e3a"><td class="mdescLeft">&#160;</td><td class="mdescRight">store_noisemap(PyX4M210 self) <br /></td></tr>
<tr class="separator:a0209fabce1f444169d3ebb57c1348e3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8aa75be9e423d3d8983a67f4857f966b"><td class="memItemLeft" align="right" valign="top"><a id="a8aa75be9e423d3d8983a67f4857f966b"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a8aa75be9e423d3d8983a67f4857f966b">delete_noisemap</a> (self)</td></tr>
<tr class="memdesc:a8aa75be9e423d3d8983a67f4857f966b"><td class="mdescLeft">&#160;</td><td class="mdescRight">delete_noisemap(PyX4M210 self) <br /></td></tr>
<tr class="separator:a8aa75be9e423d3d8983a67f4857f966b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c8cecca6e28b061434f6f21cb23b907"><td class="memItemLeft" align="right" valign="top"><a id="a4c8cecca6e28b061434f6f21cb23b907"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a4c8cecca6e28b061434f6f21cb23b907">set_noisemap_control</a> (self, noisemap_control)</td></tr>
<tr class="memdesc:a4c8cecca6e28b061434f6f21cb23b907"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_noisemap_control(PyX4M210 self, uint32_t noisemap_control) <br /></td></tr>
<tr class="separator:a4c8cecca6e28b061434f6f21cb23b907"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b63354cbae400123b81d1ec5c0fd1fe"><td class="memItemLeft" align="right" valign="top"><a id="a0b63354cbae400123b81d1ec5c0fd1fe"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0b63354cbae400123b81d1ec5c0fd1fe">get_noisemap_control</a> (self)</td></tr>
<tr class="memdesc:a0b63354cbae400123b81d1ec5c0fd1fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_noisemap_control(PyX4M210 self) -&gt; uint32_t <br /></td></tr>
<tr class="separator:a0b63354cbae400123b81d1ec5c0fd1fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cacd7e63cb2d17842126fb440cc5559"><td class="memItemLeft" align="right" valign="top"><a id="a1cacd7e63cb2d17842126fb440cc5559"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1cacd7e63cb2d17842126fb440cc5559">set_periodic_noisemap_store</a> (self, interval_minutes, reserved)</td></tr>
<tr class="memdesc:a1cacd7e63cb2d17842126fb440cc5559"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_periodic_noisemap_store(PyX4M210 self, uint32_t interval_minutes, uint32_t reserved) <br /></td></tr>
<tr class="separator:a1cacd7e63cb2d17842126fb440cc5559"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1e640e753806ac53563785bf0e43358"><td class="memItemLeft" align="right" valign="top"><a id="ab1e640e753806ac53563785bf0e43358"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab1e640e753806ac53563785bf0e43358">get_periodic_noisemap_store</a> (self)</td></tr>
<tr class="memdesc:ab1e640e753806ac53563785bf0e43358"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_periodic_noisemap_store(PyX4M210 self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_periodic_noisemap_store.xhtml" title="Representation of periodic noisemap store parameters. ">PeriodicNoisemapStore</a> <br /></td></tr>
<tr class="separator:ab1e640e753806ac53563785bf0e43358"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08dfee595c3bfd225a44d904d287e996"><td class="memItemLeft" align="right" valign="top"><a id="a08dfee595c3bfd225a44d904d287e996"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a08dfee595c3bfd225a44d904d287e996">get_parameter_file</a> (self, filename)</td></tr>
<tr class="memdesc:a08dfee595c3bfd225a44d904d287e996"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_parameter_file(PyX4M210 self, std::string const &amp; filename) -&gt; std::string <br /></td></tr>
<tr class="separator:a08dfee595c3bfd225a44d904d287e996"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf08bec26c2042ae8f8420560c89ac26"><td class="memItemLeft" align="right" valign="top"><a id="adf08bec26c2042ae8f8420560c89ac26"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adf08bec26c2042ae8f8420560c89ac26">get_profileid</a> (self)</td></tr>
<tr class="memdesc:adf08bec26c2042ae8f8420560c89ac26"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_profileid(PyX4M210 self) -&gt; uint32_t <br /></td></tr>
<tr class="separator:adf08bec26c2042ae8f8420560c89ac26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57fe31f4c6aefb920879d829f425ff6b"><td class="memItemLeft" align="right" valign="top"><a id="a57fe31f4c6aefb920879d829f425ff6b"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a57fe31f4c6aefb920879d829f425ff6b">set_parameter_file</a> (self, filename, data)</td></tr>
<tr class="memdesc:a57fe31f4c6aefb920879d829f425ff6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_parameter_file(PyX4M210 self, std::string const &amp; filename, std::string const &amp; data) <br /></td></tr>
<tr class="separator:a57fe31f4c6aefb920879d829f425ff6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0d49f580196866d0aff7b172b6670b0"><td class="memItemLeft" align="right" valign="top"><a id="ad0d49f580196866d0aff7b172b6670b0"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad0d49f580196866d0aff7b172b6670b0">set_iopin_control</a> (self, pin_id, pin_setup, pin_feature)</td></tr>
<tr class="memdesc:ad0d49f580196866d0aff7b172b6670b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_control(PyX4M210 self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature) <br /></td></tr>
<tr class="separator:ad0d49f580196866d0aff7b172b6670b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3af7caf671c18da3a01c552ee7429d0"><td class="memItemLeft" align="right" valign="top"><a id="ab3af7caf671c18da3a01c552ee7429d0"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab3af7caf671c18da3a01c552ee7429d0">get_iopin_control</a> (self, pin_id)</td></tr>
<tr class="memdesc:ab3af7caf671c18da3a01c552ee7429d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_control(PyX4M210 self, uint32_t pin_id) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_io_pin_control.xhtml" title="Representation of io pin control configuration. ">IoPinControl</a> <br /></td></tr>
<tr class="separator:ab3af7caf671c18da3a01c552ee7429d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24c14611e50da75a7325fd5dba415aa3"><td class="memItemLeft" align="right" valign="top"><a id="a24c14611e50da75a7325fd5dba415aa3"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a24c14611e50da75a7325fd5dba415aa3">set_iopin_value</a> (self, pin_id, pin_value)</td></tr>
<tr class="memdesc:a24c14611e50da75a7325fd5dba415aa3"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_value(PyX4M210 self, uint32_t pin_id, uint32_t pin_value) <br /></td></tr>
<tr class="separator:a24c14611e50da75a7325fd5dba415aa3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ecc0325b94a6ed28d2179f6bca2237a"><td class="memItemLeft" align="right" valign="top"><a id="a7ecc0325b94a6ed28d2179f6bca2237a"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7ecc0325b94a6ed28d2179f6bca2237a">get_iopin_value</a> (self, pin_id)</td></tr>
<tr class="memdesc:a7ecc0325b94a6ed28d2179f6bca2237a"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_value(PyX4M210 self, uint32_t pin_id) -&gt; uint32_t <br /></td></tr>
<tr class="separator:a7ecc0325b94a6ed28d2179f6bca2237a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a46f6d7f10cb48b9c157ba9ebd227b59e"><td class="memItemLeft" align="right" valign="top"><a id="a46f6d7f10cb48b9c157ba9ebd227b59e"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a46f6d7f10cb48b9c157ba9ebd227b59e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Proxy of C++ XeThru::PyX4M210 class. </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
