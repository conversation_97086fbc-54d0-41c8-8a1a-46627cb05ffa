<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.extras.x4_regmap_autogen.TrxCtrlMode Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>extras</b></li><li class="navelem"><b>x4_regmap_autogen</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_mode.xhtml">TrxCtrlMode</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1extras_1_1x4__regmap__autogen_1_1_trx_ctrl_mode-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.extras.x4_regmap_autogen.TrxCtrlMode Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a09a58d301ce1497783f982030698d476"><td class="memItemLeft" align="right" valign="top"><a id="a09a58d301ce1497783f982030698d476"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_tx_strobe_enable</b> (cls, map, value)</td></tr>
<tr class="separator:a09a58d301ce1497783f982030698d476"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84af81f2dddd68d227f36629dff3286b"><td class="memItemLeft" align="right" valign="top"><a id="a84af81f2dddd68d227f36629dff3286b"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_tx_strobe_enable</b> (cls, map)</td></tr>
<tr class="separator:a84af81f2dddd68d227f36629dff3286b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af25c722f1bac25029ea0e83975406ff5"><td class="memItemLeft" align="right" valign="top"><a id="af25c722f1bac25029ea0e83975406ff5"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_rx_strobe_enable</b> (cls, map, value)</td></tr>
<tr class="separator:af25c722f1bac25029ea0e83975406ff5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8cd5145cbf991eee9c37ae57e393b385"><td class="memItemLeft" align="right" valign="top"><a id="a8cd5145cbf991eee9c37ae57e393b385"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_rx_strobe_enable</b> (cls, map)</td></tr>
<tr class="separator:a8cd5145cbf991eee9c37ae57e393b385"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace1e6145442d98418af22b1041c95adc"><td class="memItemLeft" align="right" valign="top"><a id="ace1e6145442d98418af22b1041c95adc"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_phase_override</b> (cls, map, value)</td></tr>
<tr class="separator:ace1e6145442d98418af22b1041c95adc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c6f346e609ca2d117355cb5ad703952"><td class="memItemLeft" align="right" valign="top"><a id="a7c6f346e609ca2d117355cb5ad703952"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_phase_override</b> (cls, map)</td></tr>
<tr class="separator:a7c6f346e609ca2d117355cb5ad703952"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a444bc0ef9331716edcc88691bbc12e10"><td class="memItemLeft" align="right" valign="top"><a id="a444bc0ef9331716edcc88691bbc12e10"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_phase_override_val</b> (cls, map, value)</td></tr>
<tr class="separator:a444bc0ef9331716edcc88691bbc12e10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a154e870f1649d158ee4798dbceb630f4"><td class="memItemLeft" align="right" valign="top"><a id="a154e870f1649d158ee4798dbceb630f4"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_phase_override_val</b> (cls, map)</td></tr>
<tr class="separator:a154e870f1649d158ee4798dbceb630f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6371212adc2504f29ea458a859711369"><td class="memItemLeft" align="right" valign="top"><a id="a6371212adc2504f29ea458a859711369"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_send_every_pulse</b> (cls, map, value)</td></tr>
<tr class="separator:a6371212adc2504f29ea458a859711369"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9356f1cab083afa174d115e010313c79"><td class="memItemLeft" align="right" valign="top"><a id="a9356f1cab083afa174d115e010313c79"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_send_every_pulse</b> (cls, map)</td></tr>
<tr class="separator:a9356f1cab083afa174d115e010313c79"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5134972f9e6cca770ae1d84c7dbce9e0"><td class="memItemLeft" align="right" valign="top"><a id="a5134972f9e6cca770ae1d84c7dbce9e0"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_constant_clocks_per_pulse</b> (cls, map, value)</td></tr>
<tr class="separator:a5134972f9e6cca770ae1d84c7dbce9e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0708393bbee7168330fcdb4afad8886"><td class="memItemLeft" align="right" valign="top"><a id="ab0708393bbee7168330fcdb4afad8886"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_constant_clocks_per_pulse</b> (cls, map)</td></tr>
<tr class="separator:ab0708393bbee7168330fcdb4afad8886"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1397b61fc2a67f7152e1f20df4b2394b"><td class="memItemLeft" align="right" valign="top"><a id="a1397b61fc2a67f7152e1f20df4b2394b"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_ctrl_slave_mode</b> (cls, map, value)</td></tr>
<tr class="separator:a1397b61fc2a67f7152e1f20df4b2394b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a744d98db74f250733cc1c2a05a457e9e"><td class="memItemLeft" align="right" valign="top"><a id="a744d98db74f250733cc1c2a05a457e9e"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_ctrl_slave_mode</b> (cls, map)</td></tr>
<tr class="separator:a744d98db74f250733cc1c2a05a457e9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a415f04e487506821610081d4164ba15a"><td class="memItemLeft" align="right" valign="top"><a id="a415f04e487506821610081d4164ba15a"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>set_trx_start_sync_negedge</b> (cls, map, value)</td></tr>
<tr class="separator:a415f04e487506821610081d4164ba15a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2c48936a85c5cae1bc13463cb093487"><td class="memItemLeft" align="right" valign="top"><a id="ad2c48936a85c5cae1bc13463cb093487"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>get_trx_start_sync_negedge</b> (cls, map)</td></tr>
<tr class="separator:ad2c48936a85c5cae1bc13463cb093487"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8372bbf2e53dbd9daf66b71cd885401"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml#ac8372bbf2e53dbd9daf66b71cd885401">read</a> (cls, map)</td></tr>
<tr class="memdesc:ac8372bbf2e53dbd9daf66b71cd885401"><td class="mdescLeft">&#160;</td><td class="mdescRight">Read register value from chip and update shadow value.  <a href="#ac8372bbf2e53dbd9daf66b71cd885401">More...</a><br /></td></tr>
<tr class="separator:ac8372bbf2e53dbd9daf66b71cd885401"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b8ae26ad49eff0ce06dd86722414978"><td class="memItemLeft" align="right" valign="top"><a id="a6b8ae26ad49eff0ce06dd86722414978"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1extras_1_1regmap_1_1_reg.xhtml#a6b8ae26ad49eff0ce06dd86722414978">write</a> (cls, map, start, stop, size, value)</td></tr>
<tr class="memdesc:a6b8ae26ad49eff0ce06dd86722414978"><td class="mdescLeft">&#160;</td><td class="mdescRight">Update register value on chip based on new segment value. <br /></td></tr>
<tr class="separator:a6b8ae26ad49eff0ce06dd86722414978"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a9f26eb390beeaa4ecca8d661f3bca7c0"><td class="memItemLeft" align="right" valign="top"><a id="a9f26eb390beeaa4ecca8d661f3bca7c0"></a>
string&#160;</td><td class="memItemRight" valign="bottom"><b>name</b> = &quot;trx_ctrl_mode&quot;</td></tr>
<tr class="separator:a9f26eb390beeaa4ecca8d661f3bca7c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7daf60d21c3803110b8f010b7864b613"><td class="memItemLeft" align="right" valign="top"><a id="a7daf60d21c3803110b8f010b7864b613"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>address</b> = 59</td></tr>
<tr class="separator:a7daf60d21c3803110b8f010b7864b613"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76f85a068933362e9d29d2de94e60e58"><td class="memItemLeft" align="right" valign="top"><a id="a76f85a068933362e9d29d2de94e60e58"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>default</b> = 192</td></tr>
<tr class="separator:a76f85a068933362e9d29d2de94e60e58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d7a59639a9002ee6f3629c86a4b5c7d"><td class="memItemLeft" align="right" valign="top"><a id="a9d7a59639a9002ee6f3629c86a4b5c7d"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>action</b> = False</td></tr>
<tr class="separator:a9d7a59639a9002ee6f3629c86a4b5c7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a54909ea9783726f303124a292547f067"><td class="memItemLeft" align="right" valign="top"><a id="a54909ea9783726f303124a292547f067"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>logger</b> = logging.getLogger(&quot;x4regmap.pif.trx_ctrl_mode&quot;)</td></tr>
<tr class="separator:a54909ea9783726f303124a292547f067"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac8372bbf2e53dbd9daf66b71cd885401"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8372bbf2e53dbd9daf66b71cd885401">&sect;&nbsp;</a></span>read()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.extras.regmap.Reg.read </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>cls</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>map</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Read register value from chip and update shadow value. </p>
<p>Return the new register value </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/extras/x4_regmap_autogen.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
