import os
import sys
import platform

def diagnose_import():
    print("=== Python Environment Information ===")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print(f"Platform: {platform.platform()}")
    print(f"Architecture: {platform.architecture()}")
    print(f"Current working directory: {os.getcwd()}")
    
    print("\n=== Path Information ===")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    print("\n=== Testing Import Steps ===")
    
    # Test 1: Check if pymoduleconnector directory is accessible
    try:
        import pymoduleconnector
        print("✓ pymoduleconnector package imported successfully")
    except Exception as e:
        print(f"✗ Failed to import pymoduleconnector: {e}")
        return
    
    # Test 2: Check moduleconnectorwrapper directory
    try:
        import pymoduleconnector.moduleconnectorwrapper
        print("✓ pymoduleconnector.moduleconnectorwrapper package imported successfully")
    except Exception as e:
        print(f"✗ Failed to import pymoduleconnector.moduleconnectorwrapper: {e}")
        return
    
    # Test 3: Try to import the specific module that's failing
    try:
        import pymoduleconnector.moduleconnectorwrapper._moduleconnectorwrapper
        print("✓ _moduleconnectorwrapper imported successfully")
    except Exception as e:
        print(f"✗ Failed to import _moduleconnectorwrapper: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Additional debugging for DLL issues
        if "DLL load failed" in str(e):
            print("\n=== DLL Debugging ===")
            wrapper_dir = os.path.join(os.getcwd(), 'pymoduleconnector', 'moduleconnectorwrapper')
            
            # Check if we can load the .pyd file directly
            pyd_file = os.path.join(wrapper_dir, '_moduleconnectorwrapper.pyd')
            print(f"PYD file path: {pyd_file}")
            print(f"PYD file exists: {os.path.exists(pyd_file)}")
            
            # Check DLL dependencies
            dll_files = [
                'libboost_filesystem-mt.dll',
                'libboost_system-mt.dll', 
                'libgcc_s_seh-1.dll',
                'libstdc++-6.dll',
                'libwinpthread-1.dll'
            ]
            
            print("\nDLL files status:")
            for dll in dll_files:
                dll_path = os.path.join(wrapper_dir, dll)
                exists = os.path.exists(dll_path)
                print(f"  {dll}: {'EXISTS' if exists else 'MISSING'}")
                if exists:
                    size = os.path.getsize(dll_path)
                    print(f"    Size: {size} bytes")
            
            # Check if the directory is in PATH
            wrapper_dir_abs = os.path.abspath(wrapper_dir)
            path_env = os.environ.get('PATH', '')
            in_path = wrapper_dir_abs in path_env
            print(f"\nWrapper directory in PATH: {in_path}")
            print(f"Wrapper directory (absolute): {wrapper_dir_abs}")
            
            # Try adding the directory to PATH and sys.path
            print("\n=== Attempting fixes ===")
            
            # Add to sys.path
            if wrapper_dir_abs not in sys.path:
                sys.path.insert(0, wrapper_dir_abs)
                print(f"Added {wrapper_dir_abs} to sys.path")
            
            # Add to PATH environment variable
            if not in_path:
                os.environ['PATH'] = wrapper_dir_abs + os.pathsep + os.environ['PATH']
                print(f"Added {wrapper_dir_abs} to PATH")
            
            # Try import again
            try:
                import importlib
                if 'pymoduleconnector.moduleconnectorwrapper._moduleconnectorwrapper' in sys.modules:
                    del sys.modules['pymoduleconnector.moduleconnectorwrapper._moduleconnectorwrapper']
                if 'pymoduleconnector.moduleconnectorwrapper' in sys.modules:
                    importlib.reload(sys.modules['pymoduleconnector.moduleconnectorwrapper'])
                else:
                    import pymoduleconnector.moduleconnectorwrapper._moduleconnectorwrapper
                print("✓ _moduleconnectorwrapper imported successfully after fixes!")
            except Exception as e2:
                print(f"✗ Still failed after fixes: {e2}")
        
        return
    
    # Test 4: Try to import the main module
    try:
        from pymoduleconnector import ModuleConnector
        print("✓ ModuleConnector imported successfully")
    except Exception as e:
        print(f"✗ Failed to import ModuleConnector: {e}")

if __name__ == "__main__":
    diagnose_import()
