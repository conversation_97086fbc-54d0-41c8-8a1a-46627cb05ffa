import os
import sys

def fix_dll_path():
    """
    修复DLL路径问题，将moduleconnectorwrapper目录添加到PATH和sys.path中
    """
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    wrapper_dir = os.path.join(current_dir, 'pymoduleconnector', 'moduleconnectorwrapper')
    
    print(f"当前目录: {current_dir}")
    print(f"Wrapper目录: {wrapper_dir}")
    print(f"Wrapper目录存在: {os.path.exists(wrapper_dir)}")
    
    if not os.path.exists(wrapper_dir):
        print("错误: moduleconnectorwrapper目录不存在!")
        return False
    
    # 将wrapper目录添加到sys.path的开头
    if wrapper_dir not in sys.path:
        sys.path.insert(0, wrapper_dir)
        print(f"已将 {wrapper_dir} 添加到 sys.path")
    
    # 将wrapper目录添加到PATH环境变量
    path_env = os.environ.get('PATH', '')
    if wrapper_dir not in path_env:
        os.environ['PATH'] = wrapper_dir + os.pathsep + path_env
        print(f"已将 {wrapper_dir} 添加到 PATH")
    
    # 尝试导入模块
    try:
        print("\n尝试导入 pymoduleconnector...")
        import pymoduleconnector
        print("✓ pymoduleconnector 导入成功")
        
        print("尝试导入 ModuleConnector...")
        from pymoduleconnector import ModuleConnector
        print("✓ ModuleConnector 导入成功")
        
        print("\n修复成功! 现在可以正常使用 pymoduleconnector 了。")
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = fix_dll_path()
    if success:
        print("\n您现在可以在您的代码中使用以下方式导入:")
        print("from pymoduleconnector import ModuleConnector")
    else:
        print("\n修复失败，请尝试其他解决方案。")
