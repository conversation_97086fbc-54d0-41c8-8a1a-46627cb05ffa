<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">PyX4M300</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyX4M300 Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a873eeee2d3c2d5485ba82447a96c62cf">__init__</a>(self, radar_interface)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a078e8eb5598cc7c38b7822f07bd5f989">delete_noisemap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a5b4a0068b83623251b89cdfefe508e08">get_debug_output_control</a>(self, output_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aa9bb9b53f33af8a80d06d353893ca181">get_detection_zone</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#acd9819f3280ad70c955acfdf0e04504a">get_detection_zone_limits</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a8144aa7719f885017b82f9f7540fdd01">get_iopin_control</a>(self, pin_id)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a9b5e91df6369e644320d0a0ec2a2e109">get_iopin_value</a>(self, pin_id)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a222ce51e298e12567df5fd366cfab713">get_led_control</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#add9b5c1f49d7a2683f63ba8ed73c9329">get_noisemap_control</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aba7010d4b3206095c85180406e3b220b">get_output_control</a>(self, output_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2fe857eb3b74576bc60b7756b90aa2c4">get_parameter_file</a>(self, filename)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ad5b4620d151598f1b65c7c7e7d092731">get_periodic_noisemap_store</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a0d3f5832a1594e274d5efc26a47011b6">get_profileid</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a093c0d01728225c5ebb766d09c279d9c">get_sensitivity</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a660e47c44813d47ca034cda75b5e4d95">get_sensor_mode</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a358163cbc1dd10fa30679d7985db4d8e">get_system_info</a>(self, info_code)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae345e0319492b7b10d70e69b6c5a6b5c">get_tx_center_frequency</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a11942b3a8832197300bd0fa041a9b941">inject_frame</a>(self, frame_counter, frame_length, frame)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afd06c82f6163c587f6e07dc4f3ee2e8a">load_noisemap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a745a53c0deebad64c6c2d7d6ea9fc0bb">load_profile</a>(self, profileid)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae708c7b6f659138aebca25cab6dd324f">module_reset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a27b4ecdc8039088f0cd8e97ce26296f6">peek_message_baseband_ap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a100dc5ca302556fe28878a50747118ac">peek_message_baseband_iq</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#adbeba432b3601d3740dbee40f97bb603">peek_message_noisemap_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a946c49a6da188672d2eb757895f1eb0f">peek_message_noisemap_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a47634fb51ba9c6574478bcbb8a3bb7f7">peek_message_presence_movinglist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ac4031dfc7c29c40615a2f947752e9a6e">peek_message_presence_single</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a56d0a552e1170fffeea418c4834860b2">peek_message_pulsedoppler_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a28779e4c60bea75906484477d72828f5">peek_message_pulsedoppler_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ab4a434fb77c7b722316a7dfe9cb10f55">ping</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae1981310bed01af00c2a94b438100cd7">prepare_inject_frame</a>(self, num_frames, num_bins, mode)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aeb74a781f50c83a6c79ee9647c6f37a2">read_message_baseband_ap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a37a8e78bd1339fe62929623df1870012">read_message_baseband_iq</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a165b9c2579ddf56c6e61071e661e19e1">read_message_noisemap_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a393fb80799ba2a37d5904d64c874bc39">read_message_noisemap_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a4285cf9d408eaca6ee5d67b3e0d33dcc">read_message_presence_movinglist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#aaa2ada346650ef99eb0fe9ea96d690e3">read_message_presence_single</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a8df10ebe802cdd6e025148c0a4000f7d">read_message_pulsedoppler_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a7f4644b3a1d1832dea72d8b1aedb8372">read_message_pulsedoppler_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2209379a8d5e0c4d24032b8d1a1ea5fd">reset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ac26286f535f1d4a628ceec852b8a551f">reset_to_factory_preset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a2bee25ec205a5bf0eb06f356a092cc03">set_baudrate</a>(self, baudrate)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a085ffc4ab0640ab87c848059467d003f">set_debug_level</a>(self, level)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a45b0182d2b879249eb564ea193b2d82e">set_debug_output_control</a>(self, output_feature, output_control)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#adbf23c82bc1db62ea2960e16a1355040">set_detection_zone</a>(self, start, end)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a6367c4c3284b0ba6a8dfe2ebba4841d1">set_iopin_control</a>(self, pin_id, pin_setup, pin_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a0bdec90b34c48f9d253461e4eadf3255">set_iopin_value</a>(self, pin_id, pin_value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a083234422dfa614a412b9f42b30ac6e5">set_led_control</a>(self, mode, intensity)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a58bd7bc23687822d42384078ed77d775">set_noisemap_control</a>(self, noisemap_control)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afa5f8502b525d703418b815c08c767a4">set_output_control</a>(self, output_feature, output_control)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ada49c26885531b7f3965c4c006c6a804">set_parameter_file</a>(self, filename, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a07a6814f052efd83fe3d61ce96448375">set_periodic_noisemap_store</a>(self, interval_minutes, reserved)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#afe4daf3e2750c49421e4c0cb193d7eb8">set_sensitivity</a>(self, sensitivity)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a330df328fb76fd7901e76311131a3863">set_sensor_mode</a>(self, mode, param)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a30800f99e044d7f87c688706254b1cae">set_tx_center_frequency</a>(self, frequency_band)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#ae4f26481fe14f8b2d18664d87a4b84b3">start_bootloader</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a05f0c89662aa753db403fb36f7587a4f">store_noisemap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml#a142cc1bff05a309bca9188a647a5cbe7">system_run_test</a>(self, testcode, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>this</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m300.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M300</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
