import os
import sys

def check_files():
    wrapper_dir = 'pymoduleconnector/moduleconnectorwrapper'
    
    print(f"Checking directory: {wrapper_dir}")
    print(f"Directory exists: {os.path.exists(wrapper_dir)}")
    
    if os.path.exists(wrapper_dir):
        files = os.listdir(wrapper_dir)
        print(f"Files in directory: {files}")
        
        # Check for required files
        required_files = [
            '_moduleconnectorwrapper.pyd',
            'libboost_filesystem-mt.dll',
            'libboost_system-mt.dll', 
            'libgcc_s_seh-1.dll',
            'libstdc++-6.dll',
            'libwinpthread-1.dll'
        ]
        
        print("\nRequired files status:")
        for file in required_files:
            file_path = os.path.join(wrapper_dir, file)
            exists = os.path.exists(file_path)
            print(f"  {file}: {'EXISTS' if exists else 'MISSING'}")
            if exists:
                size = os.path.getsize(file_path)
                print(f"    Size: {size} bytes")
    
    # Also check current working directory
    print(f"\nCurrent working directory: {os.getcwd()}")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")

if __name__ == "__main__":
    check_files()
