"""
ModuleConnector包装模块
解决DLL加载问题的包装器
"""
import os
import sys

def setup_environment():
    """设置环境以正确加载DLL"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    wrapper_dir = os.path.join(current_dir, 'pymoduleconnector', 'moduleconnectorwrapper')
    
    # 确保wrapper目录存在
    if not os.path.exists(wrapper_dir):
        raise ImportError(f"找不到moduleconnectorwrapper目录: {wrapper_dir}")
    
    # 添加到sys.path
    if wrapper_dir not in sys.path:
        sys.path.insert(0, wrapper_dir)
    
    # 添加到PATH环境变量
    path_env = os.environ.get('PATH', '')
    if wrapper_dir not in path_env:
        os.environ['PATH'] = wrapper_dir + os.pathsep + path_env
    
    # 设置DLL目录（Python 3.8+）
    if hasattr(os, 'add_dll_directory'):
        try:
            os.add_dll_directory(wrapper_dir)
        except Exception:
            pass  # 忽略错误，继续尝试其他方法

# 在导入时自动设置环境
setup_environment()

# 现在尝试导入实际的模块
try:
    from pymoduleconnector import ModuleConnector, DataReader, DataRecorder, DataPlayer
    from pymoduleconnector import create_mc, Bootloader
    
    # 导出所有重要的类和函数
    __all__ = ['ModuleConnector', 'DataReader', 'DataRecorder', 'DataPlayer', 'create_mc', 'Bootloader']
    
    print("ModuleConnector 模块加载成功!")
    
except ImportError as e:
    print(f"警告: ModuleConnector 导入失败: {e}")
    print("请检查以下几点:")
    print("1. 确保所有DLL文件都在 pymoduleconnector/moduleconnectorwrapper/ 目录中")
    print("2. 确保Python版本兼容（建议使用Python 3.6-3.8）")
    print("3. 确保使用64位Python（如果DLL是64位的）")
    
    # 重新抛出异常
    raise

def test_connection(device_name="COM1"):
    """测试连接函数"""
    try:
        print(f"尝试连接到设备: {device_name}")
        mc = ModuleConnector(device_name, log_level=0)
        print("连接成功!")
        return mc
    except Exception as e:
        print(f"连接失败: {e}")
        return None

if __name__ == "__main__":
    print("ModuleConnector 包装模块测试")
    print("如果您看到这条消息，说明模块加载成功!")
    print("\n使用示例:")
    print("from moduleconnector_wrapper import ModuleConnector")
    print("mc = ModuleConnector('COM1')")  # 替换为您的实际端口
