<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.RadarRfData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml">RadarRfData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RadarRfData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a754a32f743ad510d8af9386156d15f97"><td class="memItemLeft" align="right" valign="top"><a id="a754a32f743ad510d8af9386156d15f97"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>__init__</b> (self, args)</td></tr>
<tr class="separator:a754a32f743ad510d8af9386156d15f97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafc6f95efeaa3b8ade825c9dda8f6642"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml#aafc6f95efeaa3b8ade825c9dda8f6642">get_data</a> (self)</td></tr>
<tr class="memdesc:aafc6f95efeaa3b8ade825c9dda8f6642"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_data(RadarRfData self) -&gt; uiVector  <a href="#aafc6f95efeaa3b8ade825c9dda8f6642">More...</a><br /></td></tr>
<tr class="separator:aafc6f95efeaa3b8ade825c9dda8f6642"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a730404138103da906a21b3141c93bfb0"><td class="memItemLeft" align="right" valign="top"><a id="a730404138103da906a21b3141c93bfb0"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:a730404138103da906a21b3141c93bfb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a320077ea50a0f02e47dde4566b162fae"><td class="memItemLeft" align="right" valign="top"><a id="a320077ea50a0f02e47dde4566b162fae"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_frame_counter_get, _moduleconnectorwrapper.RadarRfData_frame_counter_set)</td></tr>
<tr class="separator:a320077ea50a0f02e47dde4566b162fae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa29285a0ce691bec0d4b3275e8cffd46"><td class="memItemLeft" align="right" valign="top"><a id="aa29285a0ce691bec0d4b3275e8cffd46"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>num_bins</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_num_bins_get, _moduleconnectorwrapper.RadarRfData_num_bins_set)</td></tr>
<tr class="separator:aa29285a0ce691bec0d4b3275e8cffd46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa08a2a529bd083656e4f7c160cf3368c"><td class="memItemLeft" align="right" valign="top"><a id="aa08a2a529bd083656e4f7c160cf3368c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>bin_length</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_bin_length_get, _moduleconnectorwrapper.RadarRfData_bin_length_set)</td></tr>
<tr class="separator:aa08a2a529bd083656e4f7c160cf3368c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af04d8112e3fda52171d6a8786a638c20"><td class="memItemLeft" align="right" valign="top"><a id="af04d8112e3fda52171d6a8786a638c20"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sample_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_sample_frequency_get, _moduleconnectorwrapper.RadarRfData_sample_frequency_set)</td></tr>
<tr class="separator:af04d8112e3fda52171d6a8786a638c20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5df2f47f20e86a27c627d14ec2e6c90"><td class="memItemLeft" align="right" valign="top"><a id="aa5df2f47f20e86a27c627d14ec2e6c90"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>carrier_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_carrier_frequency_get, _moduleconnectorwrapper.RadarRfData_carrier_frequency_set)</td></tr>
<tr class="separator:aa5df2f47f20e86a27c627d14ec2e6c90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00c97cb7f15458f6fc69aff88625d596"><td class="memItemLeft" align="right" valign="top"><a id="a00c97cb7f15458f6fc69aff88625d596"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frames_per_second</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_frames_per_second_get, _moduleconnectorwrapper.RadarRfData_frames_per_second_set)</td></tr>
<tr class="separator:a00c97cb7f15458f6fc69aff88625d596"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21b50a154dba14a65c01779067ee27e0"><td class="memItemLeft" align="right" valign="top"><a id="a21b50a154dba14a65c01779067ee27e0"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>range_offset</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_range_offset_get, _moduleconnectorwrapper.RadarRfData_range_offset_set)</td></tr>
<tr class="separator:a21b50a154dba14a65c01779067ee27e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2057680281577cd25c3706b28890922d"><td class="memItemLeft" align="right" valign="top"><a id="a2057680281577cd25c3706b28890922d"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>data</b> = _swig_property(_moduleconnectorwrapper.RadarRfData_data_get, _moduleconnectorwrapper.RadarRfData_data_set)</td></tr>
<tr class="separator:a2057680281577cd25c3706b28890922d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aafc6f95efeaa3b8ade825c9dda8f6642"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aafc6f95efeaa3b8ade825c9dda8f6642">&sect;&nbsp;</a></span>get_data()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RadarRfData.get_data </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_data(RadarRfData self) -&gt; uiVector </p>
<p>Returns a reference to the data vector. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
