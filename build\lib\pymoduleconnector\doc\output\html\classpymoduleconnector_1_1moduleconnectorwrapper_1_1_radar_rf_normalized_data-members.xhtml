<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">RadarRfNormalizedData</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>__init__</b>(self, args) (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bin_length</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>carrier_frequency</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>data</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>frame_counter</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>frames_per_second</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml#a542fb2c1ddede9a8cffeca3473af6eef">get_data</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>num_bins</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>range_offset</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>sample_frequency</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>this</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">pymoduleconnector.moduleconnectorwrapper.RadarRfNormalizedData</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
