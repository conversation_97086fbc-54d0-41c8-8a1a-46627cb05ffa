<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml">RadarBasebandQ15Data</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a569ca42f726bbe12e110dd04fa1aaab7"><td class="memItemLeft" align="right" valign="top"><a id="a569ca42f726bbe12e110dd04fa1aaab7"></a>
def&#160;</td><td class="memItemRight" valign="bottom"><b>__init__</b> (self, args)</td></tr>
<tr class="separator:a569ca42f726bbe12e110dd04fa1aaab7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab429a717bcfc090b17c2aaf073d6fe17"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml#ab429a717bcfc090b17c2aaf073d6fe17">get_I</a> (self)</td></tr>
<tr class="memdesc:ab429a717bcfc090b17c2aaf073d6fe17"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_I(RadarBasebandQ15Data self) -&gt; sVector  <a href="#ab429a717bcfc090b17c2aaf073d6fe17">More...</a><br /></td></tr>
<tr class="separator:ab429a717bcfc090b17c2aaf073d6fe17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46e6f70585106d141c4930e310e0406c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml#a46e6f70585106d141c4930e310e0406c">get_Q</a> (self)</td></tr>
<tr class="memdesc:a46e6f70585106d141c4930e310e0406c"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_Q(RadarBasebandQ15Data self) -&gt; sVector  <a href="#a46e6f70585106d141c4930e310e0406c">More...</a><br /></td></tr>
<tr class="separator:a46e6f70585106d141c4930e310e0406c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ab58983ededbf264f160140302a900e20"><td class="memItemLeft" align="right" valign="top"><a id="ab58983ededbf264f160140302a900e20"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:ab58983ededbf264f160140302a900e20"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a86d1aa41d6b65bf9c50ccc1d1181b378"><td class="memItemLeft" align="right" valign="top"><a id="a86d1aa41d6b65bf9c50ccc1d1181b378"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frame_counter</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_frame_counter_get, _moduleconnectorwrapper.RadarBasebandQ15Data_frame_counter_set)</td></tr>
<tr class="separator:a86d1aa41d6b65bf9c50ccc1d1181b378"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75419640adbb5a52c10ef8d2b047a20c"><td class="memItemLeft" align="right" valign="top"><a id="a75419640adbb5a52c10ef8d2b047a20c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>num_bins</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_num_bins_get, _moduleconnectorwrapper.RadarBasebandQ15Data_num_bins_set)</td></tr>
<tr class="separator:a75419640adbb5a52c10ef8d2b047a20c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a5ab320e0fb760ab6ac37f7180a3261"><td class="memItemLeft" align="right" valign="top"><a id="a2a5ab320e0fb760ab6ac37f7180a3261"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>bin_length</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_bin_length_get, _moduleconnectorwrapper.RadarBasebandQ15Data_bin_length_set)</td></tr>
<tr class="separator:a2a5ab320e0fb760ab6ac37f7180a3261"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af69f3b05181c359e938b0592210cebbc"><td class="memItemLeft" align="right" valign="top"><a id="af69f3b05181c359e938b0592210cebbc"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>sample_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_sample_frequency_get, _moduleconnectorwrapper.RadarBasebandQ15Data_sample_frequency_set)</td></tr>
<tr class="separator:af69f3b05181c359e938b0592210cebbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab79480f4519eb7df0ed41646f9b8413e"><td class="memItemLeft" align="right" valign="top"><a id="ab79480f4519eb7df0ed41646f9b8413e"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>carrier_frequency</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_carrier_frequency_get, _moduleconnectorwrapper.RadarBasebandQ15Data_carrier_frequency_set)</td></tr>
<tr class="separator:ab79480f4519eb7df0ed41646f9b8413e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97f95c00146a20012c3c7398b37b135f"><td class="memItemLeft" align="right" valign="top"><a id="a97f95c00146a20012c3c7398b37b135f"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>frames_per_second</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_frames_per_second_get, _moduleconnectorwrapper.RadarBasebandQ15Data_frames_per_second_set)</td></tr>
<tr class="separator:a97f95c00146a20012c3c7398b37b135f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2dc455a0b8ace4a86d18e8e8d193f8e"><td class="memItemLeft" align="right" valign="top"><a id="ae2dc455a0b8ace4a86d18e8e8d193f8e"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>range_offset</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_range_offset_get, _moduleconnectorwrapper.RadarBasebandQ15Data_range_offset_set)</td></tr>
<tr class="separator:ae2dc455a0b8ace4a86d18e8e8d193f8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e331028fe49bfda7b9b3c156b87b337"><td class="memItemLeft" align="right" valign="top"><a id="a9e331028fe49bfda7b9b3c156b87b337"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>decimation_factor</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_decimation_factor_get, _moduleconnectorwrapper.RadarBasebandQ15Data_decimation_factor_set)</td></tr>
<tr class="separator:a9e331028fe49bfda7b9b3c156b87b337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe10bb8edec75f77ff2f03b631a9beaf"><td class="memItemLeft" align="right" valign="top"><a id="abe10bb8edec75f77ff2f03b631a9beaf"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>correction_bin</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_correction_bin_get, _moduleconnectorwrapper.RadarBasebandQ15Data_correction_bin_set)</td></tr>
<tr class="separator:abe10bb8edec75f77ff2f03b631a9beaf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a81624ecc04ed3de6a88bbe48a63261"><td class="memItemLeft" align="right" valign="top"><a id="a9a81624ecc04ed3de6a88bbe48a63261"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>correction_i</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_correction_i_get, _moduleconnectorwrapper.RadarBasebandQ15Data_correction_i_set)</td></tr>
<tr class="separator:a9a81624ecc04ed3de6a88bbe48a63261"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa09024c7d5385062aa60bc1f71273144"><td class="memItemLeft" align="right" valign="top"><a id="aa09024c7d5385062aa60bc1f71273144"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>correction_q</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_correction_q_get, _moduleconnectorwrapper.RadarBasebandQ15Data_correction_q_set)</td></tr>
<tr class="separator:aa09024c7d5385062aa60bc1f71273144"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76d027bd4924eb136e995cdbc785e23a"><td class="memItemLeft" align="right" valign="top"><a id="a76d027bd4924eb136e995cdbc785e23a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>scaling_factor</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_scaling_factor_get, _moduleconnectorwrapper.RadarBasebandQ15Data_scaling_factor_set)</td></tr>
<tr class="separator:a76d027bd4924eb136e995cdbc785e23a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e9d823b4f06a35cbfb4651ff26a2b88"><td class="memItemLeft" align="right" valign="top"><a id="a7e9d823b4f06a35cbfb4651ff26a2b88"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>i_data</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_i_data_get, _moduleconnectorwrapper.RadarBasebandQ15Data_i_data_set)</td></tr>
<tr class="separator:a7e9d823b4f06a35cbfb4651ff26a2b88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a12cfd17064f3cb66a213c78287401aad"><td class="memItemLeft" align="right" valign="top"><a id="a12cfd17064f3cb66a213c78287401aad"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>q_data</b> = _swig_property(_moduleconnectorwrapper.RadarBasebandQ15Data_q_data_get, _moduleconnectorwrapper.RadarBasebandQ15Data_q_data_set)</td></tr>
<tr class="separator:a12cfd17064f3cb66a213c78287401aad"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ab429a717bcfc090b17c2aaf073d6fe17"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab429a717bcfc090b17c2aaf073d6fe17">&sect;&nbsp;</a></span>get_I()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data.get_I </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_I(RadarBasebandQ15Data self) -&gt; sVector </p>
<p>Returns a reference to the in phase vector. </p>

</div>
</div>
<a id="a46e6f70585106d141c4930e310e0406c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a46e6f70585106d141c4930e310e0406c">&sect;&nbsp;</a></span>get_Q()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.RadarBasebandQ15Data.get_Q </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_Q(RadarBasebandQ15Data self) -&gt; sVector </p>
<p>Returns a reference to the in quadrature phase vector. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
