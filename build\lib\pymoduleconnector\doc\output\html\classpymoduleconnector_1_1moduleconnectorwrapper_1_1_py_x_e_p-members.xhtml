<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">PyXEP</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyXEP Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4e9abf9515df42cbec7e10dac634b5a2">__init__</a>(self, radar_interface)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a29bd951fa5444a8027cf53f5fccfc98b">close_file</a>(self, type, identifier, commit)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a81abaed41a0acaa22d919d339fb765f6">create_file</a>(self, file_type, identifier, length)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9acb31b30cbf7ce5f9a5c2db5a3c1d37">delete_file</a>(self, type, identifier)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a121df98d89ff511bf15092b256d19ecc">find_all_files</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3a852c9dfa49dac7dd0eb0a44d753a4c">format_filesystem</a>(self, key)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a231ece70d21ccb2807a1f75e473d848b">get_decimation_factor</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1395e4e1dc44d68e3a14f2fb9521002c">get_file</a>(self, type, identifier)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab211390dea6d5c536169c759da0fad3e">get_file_data</a>(self, type, identifier, offset, length)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a43c8064e3969ab3a04312390c600df2e">get_file_length</a>(self, type, identifier)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae820fc6d6ae63288451fb850cb665854">get_iopin_value</a>(self, pin_id)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a646697273e17f2cb85e5304fae4fc7a5">get_legacy_output</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5d7817820c63f694707659d5c5fefb95">get_normalization</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a40ea1cb1f173ae5127f156191e69ec3b">get_number_format</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aa3096c3056b61f28ac47912adc824f7f">get_phase_noise_correction</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a40dc0d608675762cd9a9b8f9feb80e4b">get_system_info</a>(self, info_code)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a51dd6c5bb894e0a018e31f36e7db4abf">module_reset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab5ff01e2a0e3d8d01be7b4373b300f99">open_file</a>(self, file_type, identifier)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9f44e9999c1cbff16c1be2c1545f7e7c">peek_message_data_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab5cf54084f1c4338d35624dda98b5170">peek_message_data_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1d8df393faf08d112804b3dc964edd23">peek_message_data_string</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#af1d52011b70768af30eaf90540c09d03">peek_message_radar_baseband_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a202dabd30b13ed77a5bab57866066be5">peek_message_radar_baseband_q15</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab4e5d8f087205d645062208b6c10565a">peek_message_radar_rf</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3ba0a566ee70eaf9780881efeedfb76f">peek_message_radar_rf_normalized</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab285a7d6d716a7b7d80be10d1e034829">peek_message_system</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8781a006a81ad3fc66dd8f1001222b0a">ping</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a6af6f6e423922d6aa891c24afc2f30f4">read_message_data_byte</a>(self, content_id, info, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad59080c40089562c07b22667a383f256">read_message_data_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a009858962f1c1a7d68235f155de10af1">read_message_data_string</a>(self, content_id, info, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aad85ad1bcf681c0181fc7357698a0f07">read_message_radar_baseband_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1ed7efc454b67aa9db7557b417b63cfb">read_message_radar_baseband_q15</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad4d188597aac757e69b07f1268cf3b7b">read_message_radar_rf</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abb483b4d0b654083d11edf73ba4b74a0">read_message_radar_rf_normalized</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a464fd568aeafaabf14099654f3305e31">read_message_system</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4e5f0a90b0ea04c1ff527b6b44696857">search_for_file_by_type</a>(self, type)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5135c26d0cf062e1f94276dc8eeafdcf">set_baudrate</a>(self, baudrate)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a16f46be3daea6dbdb1f5cb396250fd5b">set_decimation_factor</a>(self, decimation_factor)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aa7090fcdca2bdffd2fa9f06f6636c6d0">set_file</a>(self, type, identifier, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8eebb59248385899c58c6600d148dec4">set_file_data</a>(self, type, identifier, offset, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#af1cfd6f8dbeabdf195eea0be7b0c48b4">set_iopin_control</a>(self, pin_id, pin_setup, pin_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4f41fd99adcb1f5f46b6f91e7156c30f">set_iopin_value</a>(self, pin_id, pin_value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a393504b291fe0913d7d8df8c5e4c0393">set_legacy_output</a>(self, legacy_output)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4ad27bc4e4219d3f9da1112808fbd27c">set_normalization</a>(self, normalization)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad12abf8278e658d45807da0fb71a93db">set_number_format</a>(self, number_format)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a593f6400a8c812c6582e00ef56709ba0">set_phase_noise_correction</a>(self, enable, correction_distance)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>this</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a984b5fbf71975c133587c42898f345b6">x4driver_get_dac_max</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a0913355890bfe41b3ac366ff6dc2855a">x4driver_get_dac_min</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2ea1f541c7a932fb70e9a8d1bbf52b1c">x4driver_get_downconversion</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5722506858ef149aa6378ccc4198f6a8">x4driver_get_fps</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2cea78ca0d1d5f0da052f8c43e3de561">x4driver_get_frame_area</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adce1046f11e71389f7ee140e412f3cce">x4driver_get_frame_area_offset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9c7f615c64a64a8d269d412f42f266cc">x4driver_get_frame_bin_count</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8970ecf92434b784532af8cd4ac2fa7d">x4driver_get_iterations</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a901c25f7671004540dce574f1489a265">x4driver_get_pif_register</a>(self, address)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adc1d77e574da6625e2599f4e2fa3f919">x4driver_get_prf_div</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ade000d1c27bdc2a5ab625b3494d69e6c">x4driver_get_pulses_per_step</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a905538453d91219746b56abbfc154155">x4driver_get_spi_register</a>(self, address)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae3f60350e1d841761c398b949f87a333">x4driver_get_tx_center_frequency</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a27ae89ef7b96a97b0145a748c56d952b">x4driver_get_tx_power</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aace917a03ff13b718feac514d3944f97">x4driver_get_xif_register</a>(self, address)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3e0428c94e707a3bd1dd3d42c372df51">x4driver_init</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad37b0d7982bde72396eaaa1478405d69">x4driver_read_from_i2c_register</a>(self, length)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3d299d21f05a44cfc5c7254f10427e52">x4driver_read_from_spi_register</a>(self, address, length)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aec3612495db6fff46220706c4f9c2f4d">x4driver_set_dac_max</a>(self, dac_max)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abc715caef2f826b94ef3287aa153e79e">x4driver_set_dac_min</a>(self, dac_min)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a82524a9b27ab7552d999aa4b81c38cdb">x4driver_set_downconversion</a>(self, enable)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a7a7d6315f79e9e12995a884d393152b9">x4driver_set_enable</a>(self, value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a68459e2f2ee2ee0894d7df61c1757c6c">x4driver_set_fps</a>(self, fps)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab3ce265886f14f7f376e5d46b0231e0f">x4driver_set_frame_area</a>(self, start, end)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1fba61764d7abd07669e750cfe9e6981">x4driver_set_frame_area_offset</a>(self, offset)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae645c78af02a359c9f3112a664f509ca">x4driver_set_iterations</a>(self, iterations)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ac94aba38f8e7df5c5cb16a4378e37037">x4driver_set_pif_register</a>(self, address, value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1107468d06783110614842debf976d46">x4driver_set_prf_div</a>(self, prf_div)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adb925124cc48218a36c4085c5fdb83f9">x4driver_set_pulses_per_step</a>(self, pps)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a81a294b61ffa595ba5d3a36ca7aaa83d">x4driver_set_spi_register</a>(self, address, value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8d091bb042a22eb510ef2b3bb68fa7f4">x4driver_set_tx_center_frequency</a>(self, tx_frequency)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abcc14bad0b9fa3390d79d42548473afe">x4driver_set_tx_power</a>(self, tx_power)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2f1227b5306335abd50e4b41d6bc10f8">x4driver_set_xif_register</a>(self, address, value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1db60ed9c0b778ab717a6f6f6fb5ebc8">x4driver_write_to_i2c_register</a>(self, address, values)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a908f314d1aa4b2e6acac726883c66327">x4driver_write_to_spi_register</a>(self, address, values)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">pymoduleconnector.moduleconnectorwrapper.PyXEP</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
