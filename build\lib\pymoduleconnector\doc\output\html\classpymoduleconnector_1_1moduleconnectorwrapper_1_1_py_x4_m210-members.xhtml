<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">PyX4M210</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyX4M210 Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a91054f29a0a9a0dce20e687ec61ed96d">__init__</a>(self, radar_interface)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a8aa75be9e423d3d8983a67f4857f966b">delete_noisemap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aac7e55dbb655536bdacaaf535eab7bd5">get_debug_output_control</a>(self, output_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a8b4773b7c852d0c4e2c5bac8ab128f83">get_detection_zone</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a98ac4c7f8c7d54b3b31edd1abd3c2bda">get_detection_zone_limits</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab3af7caf671c18da3a01c552ee7429d0">get_iopin_control</a>(self, pin_id)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7ecc0325b94a6ed28d2179f6bca2237a">get_iopin_value</a>(self, pin_id)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a48696644525888d29cbdc919efce04c4">get_led_control</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0b63354cbae400123b81d1ec5c0fd1fe">get_noisemap_control</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7f102c853bf23c0bae7c787d93c8ef7e">get_output_control</a>(self, output_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a08dfee595c3bfd225a44d904d287e996">get_parameter_file</a>(self, filename)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab1e640e753806ac53563785bf0e43358">get_periodic_noisemap_store</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adf08bec26c2042ae8f8420560c89ac26">get_profileid</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ac4ca8d9c0cb9ba5da633a3d57305b487">get_sensitivity</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a91bb2bce178f87de0ed0a61d97b495c8">get_sensor_mode</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa825a63b84afa70e68616c11283e033a">get_system_info</a>(self, info_code)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a32cc1f185eb4f5e26c5c8211b244dbe0">get_tx_center_frequency</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a11a03e24b3a5fcd01cfa62fb7439da78">inject_frame</a>(self, frame_counter, frame_length, frame)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ac738c47adc1eda2da0844d121740402a">load_noisemap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a19cf9e9910601154f1f2f5e6e09be5d9">load_profile</a>(self, profileid)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ab257269e60d50bfcbc4cfe68a852692f">module_reset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a2de0128d8ad9bc933e0854fda0e15eaa">peek_message_baseband_ap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae8f76e7ec0075b605b969bfef486fe8d">peek_message_baseband_iq</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afb312b6ecf4684fb126fad0251bb98de">peek_message_noisemap_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a5028e2e04f37122c6979e4a0e1287ecd">peek_message_noisemap_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a34daa5adfa85a459da5116edb1451bff">peek_message_pulsedoppler_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a90814a8cac39204830af91a894b66d5f">peek_message_pulsedoppler_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a24a099f3f8f899c19ee036ea44b17bd5">peek_message_respiration_detectionlist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0eb1092c29c53f6727e5785f3c6ddf90">peek_message_respiration_legacy</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a04d9010151df0e51d40a31f145f20960">peek_message_respiration_movinglist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a9b0835578e0632256ff5503414b93db2">peek_message_respiration_normalizedmovementlist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a7a1d644b5e30c7ddf3707596d61a4b98">peek_message_respiration_sleep</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa5905b42d3f5bc3f71c8d2f267930fa2">peek_message_vital_signs</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aa79e1f79122f1436b4f3dae68134ecdf">ping</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adedfafb1eb24933c8a8a9613a43cf571">prepare_inject_frame</a>(self, num_frames, num_bins, mode)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1f8cea3e7255ec52ee7444b9ae702c8c">read_message_baseband_ap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0efb364b393388051d393d7e9853ebc3">read_message_baseband_iq</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a59cae5165608a77e836138a76322c56a">read_message_noisemap_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad240dbd66b6513c993ddba2287043d08">read_message_noisemap_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a81acb200ffc094404d15efc3fd952fb1">read_message_pulsedoppler_byte</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a475ce19a1fbdc6929a3e35e7af91ae58">read_message_pulsedoppler_float</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1f37c89243269814e4f6cbe778435548">read_message_respiration_detectionlist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a38ec0a7a881b4669d69ea38fd5bbff56">read_message_respiration_legacy</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aed360a7cf77b630cb1325606d46c5a7c">read_message_respiration_movinglist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae99b5753066a2cd2a16b99cf2e640838">read_message_respiration_normalizedmovementlist</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a96cad1f147acd0dbf4e4f8e1839d613e">read_message_respiration_sleep</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#af7babc3ff1cd64d4fba43358554b4e9f">read_message_vital_signs</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a2e55660358cf4864723cdb823a5830cb">reset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a98b89f02120573ecfb151d8474ffe069">reset_to_factory_preset</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afab145f09351f9150531e0f9a4bdac99">set_baudrate</a>(self, baudrate)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a50e5afe9d7539e47e650a48b4426081e">set_debug_level</a>(self, level)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a02b4d758a3cc6b5a111d55957e6c64a7">set_debug_output_control</a>(self, output_feature, output_control)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#adb588a48e37cce16c1d52d1caa4eb424">set_detection_zone</a>(self, start, end)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad0d49f580196866d0aff7b172b6670b0">set_iopin_control</a>(self, pin_id, pin_setup, pin_feature)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a24c14611e50da75a7325fd5dba415aa3">set_iopin_value</a>(self, pin_id, pin_value)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ad5c41bc427410450627c7645e72780ab">set_led_control</a>(self, mode, intensity)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a4c8cecca6e28b061434f6f21cb23b907">set_noisemap_control</a>(self, noisemap_control)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a90875143cc49a39ed93944d61a86a4e5">set_output_control</a>(self, output_feature, output_control)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a57fe31f4c6aefb920879d829f425ff6b">set_parameter_file</a>(self, filename, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a1cacd7e63cb2d17842126fb440cc5559">set_periodic_noisemap_store</a>(self, interval_minutes, reserved)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#aeed10f4647052d4754a04600dc8b766f">set_sensitivity</a>(self, sensitivity)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#afbc67a59155d3f0b4e69460e6b8aacc2">set_sensor_mode</a>(self, mode, param)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a9dc109f85098eded5ee441bc72e75329">set_tx_center_frequency</a>(self, frequency_band)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#ae9416a67bca04b001bda671cc194fd58">start_bootloader</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a0209fabce1f444169d3ebb57c1348e3a">store_noisemap</a>(self)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml#a473d2273063ea97d15e3c6b25d67968b">system_run_test</a>(self, testcode, data)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>this</b> (defined in <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a>)</td><td class="entry"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x4_m210.xhtml">pymoduleconnector.moduleconnectorwrapper.PyX4M210</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
