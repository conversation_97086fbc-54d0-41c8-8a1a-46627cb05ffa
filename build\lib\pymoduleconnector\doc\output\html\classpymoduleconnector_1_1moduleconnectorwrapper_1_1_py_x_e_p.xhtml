<!-- HTML header for doxygen 1.8.12-->
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.12"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>pymoduleconnector: pymoduleconnector.moduleconnectorwrapper.PyXEP Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
  <div id="top"><!-- do not remove this div, it is closed by doxygen! -->
    <!-- Platform title -->
    <div class="page-title-bar container-fluid">
      <div id="projectalign" class="row">
        <div id="projectname" class="col-sm-12">pymoduleconnector
          &#160;<span id="projectnumber">1.6.2</span>
        </div>
        <div id="projectbrief" class="col-sm-12">A Python wrapper for XeThru ModuleConnector</div>
      </div>
    </div>
    <div class="topbar">
      <div class="container">
        <div id="titlearea">
          <div class="xethru-logo">
            <img src="xethru-logo_220x55HD.png" alt="Novelda XeThru web site" id="logo-image" />
          </div>
        </div>
        <!-- end header part -->
        <!-- Generated by Doxygen 1.8.12 -->
        <!--BEGIN MAIN-NAV AND SEARCHENGINE-->
        <div id="main-nav"></div>
        <!--END MAIN-NAV AND SEARCHENGINE-->
      </div>
    </div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.12 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>pymoduleconnector</b></li><li class="navelem"><b>moduleconnectorwrapper</b></li><li class="navelem"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml">PyXEP</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p-members.xhtml">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">pymoduleconnector.moduleconnectorwrapper.PyXEP Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>C++ includes: PyXEP.hpp.  
 <a href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4e9abf9515df42cbec7e10dac634b5a2"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4e9abf9515df42cbec7e10dac634b5a2">__init__</a> (self, radar_interface)</td></tr>
<tr class="memdesc:a4e9abf9515df42cbec7e10dac634b5a2"><td class="mdescLeft">&#160;</td><td class="mdescRight"><b>init</b>(XeThru::PyXEP self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml" title="C++ includes: PyXEP.hpp. ">PyXEP</a>  <a href="#a4e9abf9515df42cbec7e10dac634b5a2">More...</a><br /></td></tr>
<tr class="separator:a4e9abf9515df42cbec7e10dac634b5a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5135c26d0cf062e1f94276dc8eeafdcf"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5135c26d0cf062e1f94276dc8eeafdcf">set_baudrate</a> (self, baudrate)</td></tr>
<tr class="memdesc:a5135c26d0cf062e1f94276dc8eeafdcf"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_baudrate(PyXEP self, uint32_t baudrate)  <a href="#a5135c26d0cf062e1f94276dc8eeafdcf">More...</a><br /></td></tr>
<tr class="separator:a5135c26d0cf062e1f94276dc8eeafdcf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51dd6c5bb894e0a018e31f36e7db4abf"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a51dd6c5bb894e0a018e31f36e7db4abf">module_reset</a> (self)</td></tr>
<tr class="memdesc:a51dd6c5bb894e0a018e31f36e7db4abf"><td class="mdescLeft">&#160;</td><td class="mdescRight">module_reset(PyXEP self)  <a href="#a51dd6c5bb894e0a018e31f36e7db4abf">More...</a><br /></td></tr>
<tr class="separator:a51dd6c5bb894e0a018e31f36e7db4abf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40dc0d608675762cd9a9b8f9feb80e4b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a40dc0d608675762cd9a9b8f9feb80e4b">get_system_info</a> (self, info_code)</td></tr>
<tr class="memdesc:a40dc0d608675762cd9a9b8f9feb80e4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_system_info(PyXEP self, uint8_t info_code) -&gt; std::string  <a href="#a40dc0d608675762cd9a9b8f9feb80e4b">More...</a><br /></td></tr>
<tr class="separator:a40dc0d608675762cd9a9b8f9feb80e4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8781a006a81ad3fc66dd8f1001222b0a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8781a006a81ad3fc66dd8f1001222b0a">ping</a> (self)</td></tr>
<tr class="memdesc:a8781a006a81ad3fc66dd8f1001222b0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">ping(PyXEP self) -&gt; uint32_t  <a href="#a8781a006a81ad3fc66dd8f1001222b0a">More...</a><br /></td></tr>
<tr class="separator:a8781a006a81ad3fc66dd8f1001222b0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ad27bc4e4219d3f9da1112808fbd27c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4ad27bc4e4219d3f9da1112808fbd27c">set_normalization</a> (self, normalization)</td></tr>
<tr class="memdesc:a4ad27bc4e4219d3f9da1112808fbd27c"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_normalization(PyXEP self, uint8_t normalization)  <a href="#a4ad27bc4e4219d3f9da1112808fbd27c">More...</a><br /></td></tr>
<tr class="separator:a4ad27bc4e4219d3f9da1112808fbd27c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d7817820c63f694707659d5c5fefb95"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5d7817820c63f694707659d5c5fefb95">get_normalization</a> (self)</td></tr>
<tr class="memdesc:a5d7817820c63f694707659d5c5fefb95"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_normalization(PyXEP self) -&gt; uint8_t  <a href="#a5d7817820c63f694707659d5c5fefb95">More...</a><br /></td></tr>
<tr class="separator:a5d7817820c63f694707659d5c5fefb95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a593f6400a8c812c6582e00ef56709ba0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a593f6400a8c812c6582e00ef56709ba0">set_phase_noise_correction</a> (self, enable, correction_distance)</td></tr>
<tr class="memdesc:a593f6400a8c812c6582e00ef56709ba0"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_phase_noise_correction(PyXEP self, uint8_t enable, float correction_distance)  <a href="#a593f6400a8c812c6582e00ef56709ba0">More...</a><br /></td></tr>
<tr class="separator:a593f6400a8c812c6582e00ef56709ba0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3096c3056b61f28ac47912adc824f7f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aa3096c3056b61f28ac47912adc824f7f">get_phase_noise_correction</a> (self)</td></tr>
<tr class="memdesc:aa3096c3056b61f28ac47912adc824f7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_phase_noise_correction(PyXEP self) -&gt; float  <a href="#aa3096c3056b61f28ac47912adc824f7f">More...</a><br /></td></tr>
<tr class="separator:aa3096c3056b61f28ac47912adc824f7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16f46be3daea6dbdb1f5cb396250fd5b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a16f46be3daea6dbdb1f5cb396250fd5b">set_decimation_factor</a> (self, decimation_factor)</td></tr>
<tr class="memdesc:a16f46be3daea6dbdb1f5cb396250fd5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_decimation_factor(PyXEP self, uint32_t decimation_factor)  <a href="#a16f46be3daea6dbdb1f5cb396250fd5b">More...</a><br /></td></tr>
<tr class="separator:a16f46be3daea6dbdb1f5cb396250fd5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a231ece70d21ccb2807a1f75e473d848b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a231ece70d21ccb2807a1f75e473d848b">get_decimation_factor</a> (self)</td></tr>
<tr class="memdesc:a231ece70d21ccb2807a1f75e473d848b"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_decimation_factor(PyXEP self) -&gt; uint32_t  <a href="#a231ece70d21ccb2807a1f75e473d848b">More...</a><br /></td></tr>
<tr class="separator:a231ece70d21ccb2807a1f75e473d848b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad12abf8278e658d45807da0fb71a93db"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad12abf8278e658d45807da0fb71a93db">set_number_format</a> (self, number_format)</td></tr>
<tr class="memdesc:ad12abf8278e658d45807da0fb71a93db"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_number_format(PyXEP self, uint8_t number_format)  <a href="#ad12abf8278e658d45807da0fb71a93db">More...</a><br /></td></tr>
<tr class="separator:ad12abf8278e658d45807da0fb71a93db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40ea1cb1f173ae5127f156191e69ec3b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a40ea1cb1f173ae5127f156191e69ec3b">get_number_format</a> (self)</td></tr>
<tr class="memdesc:a40ea1cb1f173ae5127f156191e69ec3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_number_format(PyXEP self) -&gt; uint8_t  <a href="#a40ea1cb1f173ae5127f156191e69ec3b">More...</a><br /></td></tr>
<tr class="separator:a40ea1cb1f173ae5127f156191e69ec3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a393504b291fe0913d7d8df8c5e4c0393"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a393504b291fe0913d7d8df8c5e4c0393">set_legacy_output</a> (self, legacy_output)</td></tr>
<tr class="memdesc:a393504b291fe0913d7d8df8c5e4c0393"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_legacy_output(PyXEP self, uint8_t legacy_output)  <a href="#a393504b291fe0913d7d8df8c5e4c0393">More...</a><br /></td></tr>
<tr class="separator:a393504b291fe0913d7d8df8c5e4c0393"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a646697273e17f2cb85e5304fae4fc7a5"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a646697273e17f2cb85e5304fae4fc7a5">get_legacy_output</a> (self)</td></tr>
<tr class="memdesc:a646697273e17f2cb85e5304fae4fc7a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_legacy_output(PyXEP self) -&gt; uint8_t  <a href="#a646697273e17f2cb85e5304fae4fc7a5">More...</a><br /></td></tr>
<tr class="separator:a646697273e17f2cb85e5304fae4fc7a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68459e2f2ee2ee0894d7df61c1757c6c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a68459e2f2ee2ee0894d7df61c1757c6c">x4driver_set_fps</a> (self, fps)</td></tr>
<tr class="memdesc:a68459e2f2ee2ee0894d7df61c1757c6c"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_fps(PyXEP self, float fps)  <a href="#a68459e2f2ee2ee0894d7df61c1757c6c">More...</a><br /></td></tr>
<tr class="separator:a68459e2f2ee2ee0894d7df61c1757c6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5722506858ef149aa6378ccc4198f6a8"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a5722506858ef149aa6378ccc4198f6a8">x4driver_get_fps</a> (self)</td></tr>
<tr class="memdesc:a5722506858ef149aa6378ccc4198f6a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_fps(PyXEP self) -&gt; float  <a href="#a5722506858ef149aa6378ccc4198f6a8">More...</a><br /></td></tr>
<tr class="separator:a5722506858ef149aa6378ccc4198f6a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a7d6315f79e9e12995a884d393152b9"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a7a7d6315f79e9e12995a884d393152b9">x4driver_set_enable</a> (self, value)</td></tr>
<tr class="memdesc:a7a7d6315f79e9e12995a884d393152b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_enable(PyXEP self, uint8_t value)  <a href="#a7a7d6315f79e9e12995a884d393152b9">More...</a><br /></td></tr>
<tr class="separator:a7a7d6315f79e9e12995a884d393152b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e0428c94e707a3bd1dd3d42c372df51"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3e0428c94e707a3bd1dd3d42c372df51">x4driver_init</a> (self)</td></tr>
<tr class="memdesc:a3e0428c94e707a3bd1dd3d42c372df51"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_init(PyXEP self)  <a href="#a3e0428c94e707a3bd1dd3d42c372df51">More...</a><br /></td></tr>
<tr class="separator:a3e0428c94e707a3bd1dd3d42c372df51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae645c78af02a359c9f3112a664f509ca"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae645c78af02a359c9f3112a664f509ca">x4driver_set_iterations</a> (self, iterations)</td></tr>
<tr class="memdesc:ae645c78af02a359c9f3112a664f509ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_iterations(PyXEP self, uint32_t iterations)  <a href="#ae645c78af02a359c9f3112a664f509ca">More...</a><br /></td></tr>
<tr class="separator:ae645c78af02a359c9f3112a664f509ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8970ecf92434b784532af8cd4ac2fa7d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8970ecf92434b784532af8cd4ac2fa7d">x4driver_get_iterations</a> (self)</td></tr>
<tr class="memdesc:a8970ecf92434b784532af8cd4ac2fa7d"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_iterations(PyXEP self) -&gt; uint32_t  <a href="#a8970ecf92434b784532af8cd4ac2fa7d">More...</a><br /></td></tr>
<tr class="separator:a8970ecf92434b784532af8cd4ac2fa7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb925124cc48218a36c4085c5fdb83f9"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adb925124cc48218a36c4085c5fdb83f9">x4driver_set_pulses_per_step</a> (self, pps)</td></tr>
<tr class="memdesc:adb925124cc48218a36c4085c5fdb83f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_pulses_per_step(PyXEP self, uint32_t pps)  <a href="#adb925124cc48218a36c4085c5fdb83f9">More...</a><br /></td></tr>
<tr class="separator:adb925124cc48218a36c4085c5fdb83f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade000d1c27bdc2a5ab625b3494d69e6c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ade000d1c27bdc2a5ab625b3494d69e6c">x4driver_get_pulses_per_step</a> (self)</td></tr>
<tr class="memdesc:ade000d1c27bdc2a5ab625b3494d69e6c"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_pulses_per_step(PyXEP self) -&gt; uint32_t  <a href="#ade000d1c27bdc2a5ab625b3494d69e6c">More...</a><br /></td></tr>
<tr class="separator:ade000d1c27bdc2a5ab625b3494d69e6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc715caef2f826b94ef3287aa153e79e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abc715caef2f826b94ef3287aa153e79e">x4driver_set_dac_min</a> (self, dac_min)</td></tr>
<tr class="memdesc:abc715caef2f826b94ef3287aa153e79e"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_dac_min(PyXEP self, uint32_t dac_min)  <a href="#abc715caef2f826b94ef3287aa153e79e">More...</a><br /></td></tr>
<tr class="separator:abc715caef2f826b94ef3287aa153e79e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0913355890bfe41b3ac366ff6dc2855a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a0913355890bfe41b3ac366ff6dc2855a">x4driver_get_dac_min</a> (self)</td></tr>
<tr class="memdesc:a0913355890bfe41b3ac366ff6dc2855a"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_dac_min(PyXEP self) -&gt; uint32_t  <a href="#a0913355890bfe41b3ac366ff6dc2855a">More...</a><br /></td></tr>
<tr class="separator:a0913355890bfe41b3ac366ff6dc2855a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec3612495db6fff46220706c4f9c2f4d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aec3612495db6fff46220706c4f9c2f4d">x4driver_set_dac_max</a> (self, dac_max)</td></tr>
<tr class="memdesc:aec3612495db6fff46220706c4f9c2f4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_dac_max(PyXEP self, uint32_t dac_max)  <a href="#aec3612495db6fff46220706c4f9c2f4d">More...</a><br /></td></tr>
<tr class="separator:aec3612495db6fff46220706c4f9c2f4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a984b5fbf71975c133587c42898f345b6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a984b5fbf71975c133587c42898f345b6">x4driver_get_dac_max</a> (self)</td></tr>
<tr class="memdesc:a984b5fbf71975c133587c42898f345b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_dac_max(PyXEP self) -&gt; uint32_t  <a href="#a984b5fbf71975c133587c42898f345b6">More...</a><br /></td></tr>
<tr class="separator:a984b5fbf71975c133587c42898f345b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abcc14bad0b9fa3390d79d42548473afe"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abcc14bad0b9fa3390d79d42548473afe">x4driver_set_tx_power</a> (self, tx_power)</td></tr>
<tr class="memdesc:abcc14bad0b9fa3390d79d42548473afe"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_tx_power(PyXEP self, uint8_t tx_power)  <a href="#abcc14bad0b9fa3390d79d42548473afe">More...</a><br /></td></tr>
<tr class="separator:abcc14bad0b9fa3390d79d42548473afe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27ae89ef7b96a97b0145a748c56d952b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a27ae89ef7b96a97b0145a748c56d952b">x4driver_get_tx_power</a> (self)</td></tr>
<tr class="memdesc:a27ae89ef7b96a97b0145a748c56d952b"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_tx_power(PyXEP self) -&gt; uint8_t  <a href="#a27ae89ef7b96a97b0145a748c56d952b">More...</a><br /></td></tr>
<tr class="separator:a27ae89ef7b96a97b0145a748c56d952b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82524a9b27ab7552d999aa4b81c38cdb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a82524a9b27ab7552d999aa4b81c38cdb">x4driver_set_downconversion</a> (self, enable)</td></tr>
<tr class="memdesc:a82524a9b27ab7552d999aa4b81c38cdb"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_downconversion(PyXEP self, uint8_t enable)  <a href="#a82524a9b27ab7552d999aa4b81c38cdb">More...</a><br /></td></tr>
<tr class="separator:a82524a9b27ab7552d999aa4b81c38cdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ea1f541c7a932fb70e9a8d1bbf52b1c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2ea1f541c7a932fb70e9a8d1bbf52b1c">x4driver_get_downconversion</a> (self)</td></tr>
<tr class="memdesc:a2ea1f541c7a932fb70e9a8d1bbf52b1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_downconversion(PyXEP self) -&gt; uint8_t  <a href="#a2ea1f541c7a932fb70e9a8d1bbf52b1c">More...</a><br /></td></tr>
<tr class="separator:a2ea1f541c7a932fb70e9a8d1bbf52b1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c7f615c64a64a8d269d412f42f266cc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9c7f615c64a64a8d269d412f42f266cc">x4driver_get_frame_bin_count</a> (self)</td></tr>
<tr class="memdesc:a9c7f615c64a64a8d269d412f42f266cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_frame_bin_count(PyXEP self) -&gt; uint32_t  <a href="#a9c7f615c64a64a8d269d412f42f266cc">More...</a><br /></td></tr>
<tr class="separator:a9c7f615c64a64a8d269d412f42f266cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3ce265886f14f7f376e5d46b0231e0f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab3ce265886f14f7f376e5d46b0231e0f">x4driver_set_frame_area</a> (self, start, end)</td></tr>
<tr class="memdesc:ab3ce265886f14f7f376e5d46b0231e0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_frame_area(PyXEP self, float start, float end)  <a href="#ab3ce265886f14f7f376e5d46b0231e0f">More...</a><br /></td></tr>
<tr class="separator:ab3ce265886f14f7f376e5d46b0231e0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cea78ca0d1d5f0da052f8c43e3de561"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2cea78ca0d1d5f0da052f8c43e3de561">x4driver_get_frame_area</a> (self)</td></tr>
<tr class="memdesc:a2cea78ca0d1d5f0da052f8c43e3de561"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_frame_area(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_frame_area.xhtml">FrameArea</a>  <a href="#a2cea78ca0d1d5f0da052f8c43e3de561">More...</a><br /></td></tr>
<tr class="separator:a2cea78ca0d1d5f0da052f8c43e3de561"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1fba61764d7abd07669e750cfe9e6981"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1fba61764d7abd07669e750cfe9e6981">x4driver_set_frame_area_offset</a> (self, offset)</td></tr>
<tr class="memdesc:a1fba61764d7abd07669e750cfe9e6981"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_frame_area_offset(PyXEP self, float offset)  <a href="#a1fba61764d7abd07669e750cfe9e6981">More...</a><br /></td></tr>
<tr class="separator:a1fba61764d7abd07669e750cfe9e6981"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adce1046f11e71389f7ee140e412f3cce"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adce1046f11e71389f7ee140e412f3cce">x4driver_get_frame_area_offset</a> (self)</td></tr>
<tr class="memdesc:adce1046f11e71389f7ee140e412f3cce"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_frame_area_offset(PyXEP self) -&gt; float  <a href="#adce1046f11e71389f7ee140e412f3cce">More...</a><br /></td></tr>
<tr class="separator:adce1046f11e71389f7ee140e412f3cce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d091bb042a22eb510ef2b3bb68fa7f4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8d091bb042a22eb510ef2b3bb68fa7f4">x4driver_set_tx_center_frequency</a> (self, tx_frequency)</td></tr>
<tr class="memdesc:a8d091bb042a22eb510ef2b3bb68fa7f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_tx_center_frequency(PyXEP self, uint8_t tx_frequency)  <a href="#a8d091bb042a22eb510ef2b3bb68fa7f4">More...</a><br /></td></tr>
<tr class="separator:a8d091bb042a22eb510ef2b3bb68fa7f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3f60350e1d841761c398b949f87a333"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae3f60350e1d841761c398b949f87a333">x4driver_get_tx_center_frequency</a> (self)</td></tr>
<tr class="memdesc:ae3f60350e1d841761c398b949f87a333"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_tx_center_frequency(PyXEP self) -&gt; uint8_t  <a href="#ae3f60350e1d841761c398b949f87a333">More...</a><br /></td></tr>
<tr class="separator:ae3f60350e1d841761c398b949f87a333"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81a294b61ffa595ba5d3a36ca7aaa83d"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a81a294b61ffa595ba5d3a36ca7aaa83d">x4driver_set_spi_register</a> (self, address, value)</td></tr>
<tr class="memdesc:a81a294b61ffa595ba5d3a36ca7aaa83d"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_spi_register(PyXEP self, uint8_t address, uint8_t value)  <a href="#a81a294b61ffa595ba5d3a36ca7aaa83d">More...</a><br /></td></tr>
<tr class="separator:a81a294b61ffa595ba5d3a36ca7aaa83d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a905538453d91219746b56abbfc154155"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a905538453d91219746b56abbfc154155">x4driver_get_spi_register</a> (self, address)</td></tr>
<tr class="memdesc:a905538453d91219746b56abbfc154155"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_spi_register(PyXEP self, uint8_t address) -&gt; uint8_t  <a href="#a905538453d91219746b56abbfc154155">More...</a><br /></td></tr>
<tr class="separator:a905538453d91219746b56abbfc154155"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a908f314d1aa4b2e6acac726883c66327"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a908f314d1aa4b2e6acac726883c66327">x4driver_write_to_spi_register</a> (self, address, values)</td></tr>
<tr class="memdesc:a908f314d1aa4b2e6acac726883c66327"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_write_to_spi_register(PyXEP self, uint8_t address, ucVector values)  <a href="#a908f314d1aa4b2e6acac726883c66327">More...</a><br /></td></tr>
<tr class="separator:a908f314d1aa4b2e6acac726883c66327"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d299d21f05a44cfc5c7254f10427e52"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3d299d21f05a44cfc5c7254f10427e52">x4driver_read_from_spi_register</a> (self, address, length)</td></tr>
<tr class="memdesc:a3d299d21f05a44cfc5c7254f10427e52"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_read_from_spi_register(PyXEP self, uint8_t address, uint32_t const length) -&gt; ucVector  <a href="#a3d299d21f05a44cfc5c7254f10427e52">More...</a><br /></td></tr>
<tr class="separator:a3d299d21f05a44cfc5c7254f10427e52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1db60ed9c0b778ab717a6f6f6fb5ebc8"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1db60ed9c0b778ab717a6f6f6fb5ebc8">x4driver_write_to_i2c_register</a> (self, address, values)</td></tr>
<tr class="memdesc:a1db60ed9c0b778ab717a6f6f6fb5ebc8"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_write_to_i2c_register(PyXEP self, uint8_t address, ucVector values)  <a href="#a1db60ed9c0b778ab717a6f6f6fb5ebc8">More...</a><br /></td></tr>
<tr class="separator:a1db60ed9c0b778ab717a6f6f6fb5ebc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad37b0d7982bde72396eaaa1478405d69"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad37b0d7982bde72396eaaa1478405d69">x4driver_read_from_i2c_register</a> (self, length)</td></tr>
<tr class="memdesc:ad37b0d7982bde72396eaaa1478405d69"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_read_from_i2c_register(PyXEP self, uint32_t const length) -&gt; ucVector  <a href="#ad37b0d7982bde72396eaaa1478405d69">More...</a><br /></td></tr>
<tr class="separator:ad37b0d7982bde72396eaaa1478405d69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac94aba38f8e7df5c5cb16a4378e37037"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ac94aba38f8e7df5c5cb16a4378e37037">x4driver_set_pif_register</a> (self, address, value)</td></tr>
<tr class="memdesc:ac94aba38f8e7df5c5cb16a4378e37037"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_pif_register(PyXEP self, uint8_t address, uint8_t value)  <a href="#ac94aba38f8e7df5c5cb16a4378e37037">More...</a><br /></td></tr>
<tr class="separator:ac94aba38f8e7df5c5cb16a4378e37037"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a901c25f7671004540dce574f1489a265"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a901c25f7671004540dce574f1489a265">x4driver_get_pif_register</a> (self, address)</td></tr>
<tr class="memdesc:a901c25f7671004540dce574f1489a265"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_pif_register(PyXEP self, uint8_t address) -&gt; uint8_t  <a href="#a901c25f7671004540dce574f1489a265">More...</a><br /></td></tr>
<tr class="separator:a901c25f7671004540dce574f1489a265"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f1227b5306335abd50e4b41d6bc10f8"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a2f1227b5306335abd50e4b41d6bc10f8">x4driver_set_xif_register</a> (self, address, value)</td></tr>
<tr class="memdesc:a2f1227b5306335abd50e4b41d6bc10f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_xif_register(PyXEP self, uint8_t address, uint8_t value)  <a href="#a2f1227b5306335abd50e4b41d6bc10f8">More...</a><br /></td></tr>
<tr class="separator:a2f1227b5306335abd50e4b41d6bc10f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace917a03ff13b718feac514d3944f97"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aace917a03ff13b718feac514d3944f97">x4driver_get_xif_register</a> (self, address)</td></tr>
<tr class="memdesc:aace917a03ff13b718feac514d3944f97"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_xif_register(PyXEP self, uint8_t address) -&gt; uint8_t  <a href="#aace917a03ff13b718feac514d3944f97">More...</a><br /></td></tr>
<tr class="separator:aace917a03ff13b718feac514d3944f97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1107468d06783110614842debf976d46"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1107468d06783110614842debf976d46">x4driver_set_prf_div</a> (self, prf_div)</td></tr>
<tr class="memdesc:a1107468d06783110614842debf976d46"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_set_prf_div(PyXEP self, uint8_t prf_div)  <a href="#a1107468d06783110614842debf976d46">More...</a><br /></td></tr>
<tr class="separator:a1107468d06783110614842debf976d46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc1d77e574da6625e2599f4e2fa3f919"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#adc1d77e574da6625e2599f4e2fa3f919">x4driver_get_prf_div</a> (self)</td></tr>
<tr class="memdesc:adc1d77e574da6625e2599f4e2fa3f919"><td class="mdescLeft">&#160;</td><td class="mdescRight">x4driver_get_prf_div(PyXEP self) -&gt; uint8_t  <a href="#adc1d77e574da6625e2599f4e2fa3f919">More...</a><br /></td></tr>
<tr class="separator:adc1d77e574da6625e2599f4e2fa3f919"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1cfd6f8dbeabdf195eea0be7b0c48b4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#af1cfd6f8dbeabdf195eea0be7b0c48b4">set_iopin_control</a> (self, pin_id, pin_setup, pin_feature)</td></tr>
<tr class="memdesc:af1cfd6f8dbeabdf195eea0be7b0c48b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_control(PyXEP self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature)  <a href="#af1cfd6f8dbeabdf195eea0be7b0c48b4">More...</a><br /></td></tr>
<tr class="separator:af1cfd6f8dbeabdf195eea0be7b0c48b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f41fd99adcb1f5f46b6f91e7156c30f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4f41fd99adcb1f5f46b6f91e7156c30f">set_iopin_value</a> (self, pin_id, pin_value)</td></tr>
<tr class="memdesc:a4f41fd99adcb1f5f46b6f91e7156c30f"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_iopin_value(PyXEP self, uint32_t pin_id, uint32_t pin_value)  <a href="#a4f41fd99adcb1f5f46b6f91e7156c30f">More...</a><br /></td></tr>
<tr class="separator:a4f41fd99adcb1f5f46b6f91e7156c30f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae820fc6d6ae63288451fb850cb665854"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ae820fc6d6ae63288451fb850cb665854">get_iopin_value</a> (self, pin_id)</td></tr>
<tr class="memdesc:ae820fc6d6ae63288451fb850cb665854"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_iopin_value(PyXEP self, uint32_t pin_id) -&gt; uint32_t  <a href="#ae820fc6d6ae63288451fb850cb665854">More...</a><br /></td></tr>
<tr class="separator:ae820fc6d6ae63288451fb850cb665854"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5cf54084f1c4338d35624dda98b5170"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab5cf54084f1c4338d35624dda98b5170">peek_message_data_float</a> (self)</td></tr>
<tr class="memdesc:ab5cf54084f1c4338d35624dda98b5170"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_data_float(PyXEP self) -&gt; int  <a href="#ab5cf54084f1c4338d35624dda98b5170">More...</a><br /></td></tr>
<tr class="separator:ab5cf54084f1c4338d35624dda98b5170"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad59080c40089562c07b22667a383f256"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad59080c40089562c07b22667a383f256">read_message_data_float</a> (self)</td></tr>
<tr class="memdesc:ad59080c40089562c07b22667a383f256"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_data_float(PyXEP self) -&gt; DataFloat  <a href="#ad59080c40089562c07b22667a383f256">More...</a><br /></td></tr>
<tr class="separator:ad59080c40089562c07b22667a383f256"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4e5d8f087205d645062208b6c10565a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab4e5d8f087205d645062208b6c10565a">peek_message_radar_rf</a> (self)</td></tr>
<tr class="memdesc:ab4e5d8f087205d645062208b6c10565a"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_radar_rf(PyXEP self) -&gt; int  <a href="#ab4e5d8f087205d645062208b6c10565a">More...</a><br /></td></tr>
<tr class="separator:ab4e5d8f087205d645062208b6c10565a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4d188597aac757e69b07f1268cf3b7b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ad4d188597aac757e69b07f1268cf3b7b">read_message_radar_rf</a> (self)</td></tr>
<tr class="memdesc:ad4d188597aac757e69b07f1268cf3b7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_radar_rf(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml">RadarRfData</a>  <a href="#ad4d188597aac757e69b07f1268cf3b7b">More...</a><br /></td></tr>
<tr class="separator:ad4d188597aac757e69b07f1268cf3b7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ba0a566ee70eaf9780881efeedfb76f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3ba0a566ee70eaf9780881efeedfb76f">peek_message_radar_rf_normalized</a> (self)</td></tr>
<tr class="memdesc:a3ba0a566ee70eaf9780881efeedfb76f"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_radar_rf_normalized(PyXEP self) -&gt; int  <a href="#a3ba0a566ee70eaf9780881efeedfb76f">More...</a><br /></td></tr>
<tr class="separator:a3ba0a566ee70eaf9780881efeedfb76f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb483b4d0b654083d11edf73ba4b74a0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#abb483b4d0b654083d11edf73ba4b74a0">read_message_radar_rf_normalized</a> (self)</td></tr>
<tr class="memdesc:abb483b4d0b654083d11edf73ba4b74a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_radar_rf_normalized(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">RadarRfNormalizedData</a>  <a href="#abb483b4d0b654083d11edf73ba4b74a0">More...</a><br /></td></tr>
<tr class="separator:abb483b4d0b654083d11edf73ba4b74a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1d52011b70768af30eaf90540c09d03"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#af1d52011b70768af30eaf90540c09d03">peek_message_radar_baseband_float</a> (self)</td></tr>
<tr class="memdesc:af1d52011b70768af30eaf90540c09d03"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_radar_baseband_float(PyXEP self) -&gt; int  <a href="#af1d52011b70768af30eaf90540c09d03">More...</a><br /></td></tr>
<tr class="separator:af1d52011b70768af30eaf90540c09d03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad85ad1bcf681c0181fc7357698a0f07"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aad85ad1bcf681c0181fc7357698a0f07">read_message_radar_baseband_float</a> (self)</td></tr>
<tr class="memdesc:aad85ad1bcf681c0181fc7357698a0f07"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_radar_baseband_float(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml">RadarBasebandFloatData</a>  <a href="#aad85ad1bcf681c0181fc7357698a0f07">More...</a><br /></td></tr>
<tr class="separator:aad85ad1bcf681c0181fc7357698a0f07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a202dabd30b13ed77a5bab57866066be5"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a202dabd30b13ed77a5bab57866066be5">peek_message_radar_baseband_q15</a> (self)</td></tr>
<tr class="memdesc:a202dabd30b13ed77a5bab57866066be5"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_radar_baseband_q15(PyXEP self) -&gt; int  <a href="#a202dabd30b13ed77a5bab57866066be5">More...</a><br /></td></tr>
<tr class="separator:a202dabd30b13ed77a5bab57866066be5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ed7efc454b67aa9db7557b417b63cfb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1ed7efc454b67aa9db7557b417b63cfb">read_message_radar_baseband_q15</a> (self)</td></tr>
<tr class="memdesc:a1ed7efc454b67aa9db7557b417b63cfb"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_radar_baseband_q15(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml">RadarBasebandQ15Data</a>  <a href="#a1ed7efc454b67aa9db7557b417b63cfb">More...</a><br /></td></tr>
<tr class="separator:a1ed7efc454b67aa9db7557b417b63cfb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d8df393faf08d112804b3dc964edd23"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1d8df393faf08d112804b3dc964edd23">peek_message_data_string</a> (self)</td></tr>
<tr class="memdesc:a1d8df393faf08d112804b3dc964edd23"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_data_string(PyXEP self) -&gt; int  <a href="#a1d8df393faf08d112804b3dc964edd23">More...</a><br /></td></tr>
<tr class="separator:a1d8df393faf08d112804b3dc964edd23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a009858962f1c1a7d68235f155de10af1"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a009858962f1c1a7d68235f155de10af1">read_message_data_string</a> (self, content_id, info, data)</td></tr>
<tr class="memdesc:a009858962f1c1a7d68235f155de10af1"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_data_string(PyXEP self, uint32_t * content_id, uint32_t * info, std::string * data) -&gt; int  <a href="#a009858962f1c1a7d68235f155de10af1">More...</a><br /></td></tr>
<tr class="separator:a009858962f1c1a7d68235f155de10af1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f44e9999c1cbff16c1be2c1545f7e7c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9f44e9999c1cbff16c1be2c1545f7e7c">peek_message_data_byte</a> (self)</td></tr>
<tr class="memdesc:a9f44e9999c1cbff16c1be2c1545f7e7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_data_byte(PyXEP self) -&gt; int  <a href="#a9f44e9999c1cbff16c1be2c1545f7e7c">More...</a><br /></td></tr>
<tr class="separator:a9f44e9999c1cbff16c1be2c1545f7e7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6af6f6e423922d6aa891c24afc2f30f4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a6af6f6e423922d6aa891c24afc2f30f4">read_message_data_byte</a> (self, content_id, info, data)</td></tr>
<tr class="memdesc:a6af6f6e423922d6aa891c24afc2f30f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_data_byte(PyXEP self, uint32_t * content_id, uint32_t * info, ucVector data) -&gt; int  <a href="#a6af6f6e423922d6aa891c24afc2f30f4">More...</a><br /></td></tr>
<tr class="separator:a6af6f6e423922d6aa891c24afc2f30f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab285a7d6d716a7b7d80be10d1e034829"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab285a7d6d716a7b7d80be10d1e034829">peek_message_system</a> (self)</td></tr>
<tr class="memdesc:ab285a7d6d716a7b7d80be10d1e034829"><td class="mdescLeft">&#160;</td><td class="mdescRight">peek_message_system(PyXEP self) -&gt; int  <a href="#ab285a7d6d716a7b7d80be10d1e034829">More...</a><br /></td></tr>
<tr class="separator:ab285a7d6d716a7b7d80be10d1e034829"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a464fd568aeafaabf14099654f3305e31"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a464fd568aeafaabf14099654f3305e31">read_message_system</a> (self)</td></tr>
<tr class="memdesc:a464fd568aeafaabf14099654f3305e31"><td class="mdescLeft">&#160;</td><td class="mdescRight">read_message_system(PyXEP self) -&gt; uint32_t  <a href="#a464fd568aeafaabf14099654f3305e31">More...</a><br /></td></tr>
<tr class="separator:a464fd568aeafaabf14099654f3305e31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e5f0a90b0ea04c1ff527b6b44696857"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a4e5f0a90b0ea04c1ff527b6b44696857">search_for_file_by_type</a> (self, type)</td></tr>
<tr class="memdesc:a4e5f0a90b0ea04c1ff527b6b44696857"><td class="mdescLeft">&#160;</td><td class="mdescRight">search_for_file_by_type(PyXEP self, uint32_t type) -&gt; iVector  <a href="#a4e5f0a90b0ea04c1ff527b6b44696857">More...</a><br /></td></tr>
<tr class="separator:a4e5f0a90b0ea04c1ff527b6b44696857"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a121df98d89ff511bf15092b256d19ecc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a121df98d89ff511bf15092b256d19ecc">find_all_files</a> (self)</td></tr>
<tr class="memdesc:a121df98d89ff511bf15092b256d19ecc"><td class="mdescLeft">&#160;</td><td class="mdescRight">find_all_files(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml">Files</a>  <a href="#a121df98d89ff511bf15092b256d19ecc">More...</a><br /></td></tr>
<tr class="separator:a121df98d89ff511bf15092b256d19ecc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81abaed41a0acaa22d919d339fb765f6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a81abaed41a0acaa22d919d339fb765f6">create_file</a> (self, file_type, identifier, length)</td></tr>
<tr class="memdesc:a81abaed41a0acaa22d919d339fb765f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">create_file(PyXEP self, uint32_t file_type, uint32_t identifier, uint32_t length)  <a href="#a81abaed41a0acaa22d919d339fb765f6">More...</a><br /></td></tr>
<tr class="separator:a81abaed41a0acaa22d919d339fb765f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5ff01e2a0e3d8d01be7b4373b300f99"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab5ff01e2a0e3d8d01be7b4373b300f99">open_file</a> (self, file_type, identifier)</td></tr>
<tr class="memdesc:ab5ff01e2a0e3d8d01be7b4373b300f99"><td class="mdescLeft">&#160;</td><td class="mdescRight">open_file(PyXEP self, uint32_t file_type, uint32_t identifier)  <a href="#ab5ff01e2a0e3d8d01be7b4373b300f99">More...</a><br /></td></tr>
<tr class="separator:ab5ff01e2a0e3d8d01be7b4373b300f99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8eebb59248385899c58c6600d148dec4"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a8eebb59248385899c58c6600d148dec4">set_file_data</a> (self, type, identifier, offset, data)</td></tr>
<tr class="memdesc:a8eebb59248385899c58c6600d148dec4"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_file_data(PyXEP self, uint32_t type, uint32_t identifier, uint32_t offset, ucVector data)  <a href="#a8eebb59248385899c58c6600d148dec4">More...</a><br /></td></tr>
<tr class="separator:a8eebb59248385899c58c6600d148dec4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29bd951fa5444a8027cf53f5fccfc98b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a29bd951fa5444a8027cf53f5fccfc98b">close_file</a> (self, type, identifier, commit)</td></tr>
<tr class="memdesc:a29bd951fa5444a8027cf53f5fccfc98b"><td class="mdescLeft">&#160;</td><td class="mdescRight">close_file(PyXEP self, uint32_t type, uint32_t identifier, bool commit)  <a href="#a29bd951fa5444a8027cf53f5fccfc98b">More...</a><br /></td></tr>
<tr class="separator:a29bd951fa5444a8027cf53f5fccfc98b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43c8064e3969ab3a04312390c600df2e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a43c8064e3969ab3a04312390c600df2e">get_file_length</a> (self, type, identifier)</td></tr>
<tr class="memdesc:a43c8064e3969ab3a04312390c600df2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_file_length(PyXEP self, uint32_t type, uint32_t identifier) -&gt; uint32_t  <a href="#a43c8064e3969ab3a04312390c600df2e">More...</a><br /></td></tr>
<tr class="separator:a43c8064e3969ab3a04312390c600df2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9acb31b30cbf7ce5f9a5c2db5a3c1d37"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a9acb31b30cbf7ce5f9a5c2db5a3c1d37">delete_file</a> (self, type, identifier)</td></tr>
<tr class="memdesc:a9acb31b30cbf7ce5f9a5c2db5a3c1d37"><td class="mdescLeft">&#160;</td><td class="mdescRight">delete_file(PyXEP self, uint32_t type, uint32_t identifier)  <a href="#a9acb31b30cbf7ce5f9a5c2db5a3c1d37">More...</a><br /></td></tr>
<tr class="separator:a9acb31b30cbf7ce5f9a5c2db5a3c1d37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a852c9dfa49dac7dd0eb0a44d753a4c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a3a852c9dfa49dac7dd0eb0a44d753a4c">format_filesystem</a> (self, key)</td></tr>
<tr class="memdesc:a3a852c9dfa49dac7dd0eb0a44d753a4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">format_filesystem(PyXEP self, uint32_t key)  <a href="#a3a852c9dfa49dac7dd0eb0a44d753a4c">More...</a><br /></td></tr>
<tr class="separator:a3a852c9dfa49dac7dd0eb0a44d753a4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab211390dea6d5c536169c759da0fad3e"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#ab211390dea6d5c536169c759da0fad3e">get_file_data</a> (self, type, identifier, offset, length)</td></tr>
<tr class="memdesc:ab211390dea6d5c536169c759da0fad3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_file_data(PyXEP self, uint32_t type, uint32_t identifier, uint32_t offset, uint32_t length) -&gt; ucVector  <a href="#ab211390dea6d5c536169c759da0fad3e">More...</a><br /></td></tr>
<tr class="separator:ab211390dea6d5c536169c759da0fad3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7090fcdca2bdffd2fa9f06f6636c6d0"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#aa7090fcdca2bdffd2fa9f06f6636c6d0">set_file</a> (self, type, identifier, data)</td></tr>
<tr class="memdesc:aa7090fcdca2bdffd2fa9f06f6636c6d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">set_file(PyXEP self, uint32_t type, uint32_t identifier, ucVector data)  <a href="#aa7090fcdca2bdffd2fa9f06f6636c6d0">More...</a><br /></td></tr>
<tr class="separator:aa7090fcdca2bdffd2fa9f06f6636c6d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1395e4e1dc44d68e3a14f2fb9521002c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml#a1395e4e1dc44d68e3a14f2fb9521002c">get_file</a> (self, type, identifier)</td></tr>
<tr class="memdesc:a1395e4e1dc44d68e3a14f2fb9521002c"><td class="mdescLeft">&#160;</td><td class="mdescRight">get_file(PyXEP self, uint32_t type, uint32_t identifier) -&gt; ucVector  <a href="#a1395e4e1dc44d68e3a14f2fb9521002c">More...</a><br /></td></tr>
<tr class="separator:a1395e4e1dc44d68e3a14f2fb9521002c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ae6f541315166995bc919f85f29108beb"><td class="memItemLeft" align="right" valign="top"><a id="ae6f541315166995bc919f85f29108beb"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>this</b></td></tr>
<tr class="separator:ae6f541315166995bc919f85f29108beb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>C++ includes: PyXEP.hpp. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a4e9abf9515df42cbec7e10dac634b5a2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e9abf9515df42cbec7e10dac634b5a2">&sect;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>radar_interface</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p><b>init</b>(XeThru::PyXEP self, LockedRadarInterfacePtr &amp; radar_interface) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_py_x_e_p.xhtml" title="C++ includes: PyXEP.hpp. ">PyXEP</a> </p>
<p>Constructor.</p>
<p>Initialized by <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_python_module_connector.xhtml#a6c38a6ca30e37b27412eef559f3dddd4" title="get_xep(PythonModuleConnector self) -&gt; PyXEP ">PythonModuleConnector::get_xep</a></p>
<h2>Parameters </h2>
<ul>
<li><code>radar_interface</code> : a reference to the internal radar interface </li>
</ul>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a29bd951fa5444a8027cf53f5fccfc98b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29bd951fa5444a8027cf53f5fccfc98b">&sect;&nbsp;</a></span>close_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.close_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>commit</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>close_file(PyXEP self, uint32_t type, uint32_t identifier, bool commit) </p>
<p>Close the file.</p>
<h2>Parameters </h2>
<ul>
<li><code>type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file.</li>
<li><code>commit</code> : wether to commit changes to the file to close </li>
</ul>

</div>
</div>
<a id="a81abaed41a0acaa22d919d339fb765f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81abaed41a0acaa22d919d339fb765f6">&sect;&nbsp;</a></span>create_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.create_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>file_type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>create_file(PyXEP self, uint32_t file_type, uint32_t identifier, uint32_t length) </p>
<p>Creates and opens a new file with given type, identifiers and length.</p>
<h2>Parameters </h2>
<ul>
<li><code>file_type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file.</li>
<li><code>length</code> : length of the file to create </li>
</ul>

</div>
</div>
<a id="a9acb31b30cbf7ce5f9a5c2db5a3c1d37"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9acb31b30cbf7ce5f9a5c2db5a3c1d37">&sect;&nbsp;</a></span>delete_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.delete_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>delete_file(PyXEP self, uint32_t type, uint32_t identifier) </p>
<p>Deletes a file.</p>
<h2>Parameters </h2>
<ul>
<li><code>type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file. </li>
</ul>

</div>
</div>
<a id="a121df98d89ff511bf15092b256d19ecc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a121df98d89ff511bf15092b256d19ecc">&sect;&nbsp;</a></span>find_all_files()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.find_all_files </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>find_all_files(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_files.xhtml">Files</a> </p>
<p>Finds all files and stores the type and identifier of each file in the given buffers.</p>
<h2>Returns </h2>
<p>The result from the search. A map of file (type identifiers) </p>

</div>
</div>
<a id="a3a852c9dfa49dac7dd0eb0a44d753a4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a852c9dfa49dac7dd0eb0a44d753a4c">&sect;&nbsp;</a></span>format_filesystem()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.format_filesystem </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>key</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>format_filesystem(PyXEP self, uint32_t key) </p>

</div>
</div>
<a id="a231ece70d21ccb2807a1f75e473d848b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a231ece70d21ccb2807a1f75e473d848b">&sect;&nbsp;</a></span>get_decimation_factor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_decimation_factor </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_decimation_factor(PyXEP self) -&gt; uint32_t </p>

</div>
</div>
<a id="a1395e4e1dc44d68e3a14f2fb9521002c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1395e4e1dc44d68e3a14f2fb9521002c">&sect;&nbsp;</a></span>get_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_file(PyXEP self, uint32_t type, uint32_t identifier) -&gt; ucVector </p>

</div>
</div>
<a id="ab211390dea6d5c536169c759da0fad3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab211390dea6d5c536169c759da0fad3e">&sect;&nbsp;</a></span>get_file_data()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_file_data </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>offset</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_file_data(PyXEP self, uint32_t type, uint32_t identifier, uint32_t offset, uint32_t length) -&gt; ucVector </p>
<p>Reads length number of bytes of the file from the offset.</p>
<h2>Parameters </h2>
<ul>
<li><code>type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file.</li>
<li><code>offset</code> : The offset to read from.</li>
<li><code>length</code> : Number of bytes to read.</li>
</ul>
<h2>Returns </h2>
<p>The result as a vector of bytes. </p>

</div>
</div>
<a id="a43c8064e3969ab3a04312390c600df2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a43c8064e3969ab3a04312390c600df2e">&sect;&nbsp;</a></span>get_file_length()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_file_length </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_file_length(PyXEP self, uint32_t type, uint32_t identifier) -&gt; uint32_t </p>
<p>Gets the length of a file.</p>
<h2>Parameters </h2>
<ul>
<li><code>type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file.</li>
</ul>
<h2>Returns </h2>
<p>The length of the file. </p>

</div>
</div>
<a id="ae820fc6d6ae63288451fb850cb665854"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae820fc6d6ae63288451fb850cb665854">&sect;&nbsp;</a></span>get_iopin_value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_iopin_value </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_iopin_value(PyXEP self, uint32_t pin_id) -&gt; uint32_t </p>
<p>Read IO pin level or value.</p>
<h2>Returns </h2>
<p>value io pin value </p>

</div>
</div>
<a id="a646697273e17f2cb85e5304fae4fc7a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a646697273e17f2cb85e5304fae4fc7a5">&sect;&nbsp;</a></span>get_legacy_output()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_legacy_output </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_legacy_output(PyXEP self) -&gt; uint8_t </p>

</div>
</div>
<a id="a5d7817820c63f694707659d5c5fefb95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d7817820c63f694707659d5c5fefb95">&sect;&nbsp;</a></span>get_normalization()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_normalization </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_normalization(PyXEP self) -&gt; uint8_t </p>

</div>
</div>
<a id="a40ea1cb1f173ae5127f156191e69ec3b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40ea1cb1f173ae5127f156191e69ec3b">&sect;&nbsp;</a></span>get_number_format()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_number_format </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_number_format(PyXEP self) -&gt; uint8_t </p>

</div>
</div>
<a id="aa3096c3056b61f28ac47912adc824f7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa3096c3056b61f28ac47912adc824f7f">&sect;&nbsp;</a></span>get_phase_noise_correction()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_phase_noise_correction </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_phase_noise_correction(PyXEP self) -&gt; float </p>

</div>
</div>
<a id="a40dc0d608675762cd9a9b8f9feb80e4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40dc0d608675762cd9a9b8f9feb80e4b">&sect;&nbsp;</a></span>get_system_info()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.get_system_info </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>info_code</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>get_system_info(PyXEP self, uint8_t info_code) -&gt; std::string </p>
<h2>Parameters </h2>
<ul>
<li><code>info_code</code> : Specifies the info code.</li>
</ul>
<h2>Returns </h2>
<p>a string containing system information given by infocode: XTS_SSIC_FIRMWAREID = 0x02 -&gt; Returns the installed Firmware ID, "XEP" XTS_SSIC_VERSION = 0x03 -&gt; Returns the installed Firmware Version. As viewed from the "highest" level of the software. XTS_SSIC_BUILD = 0x04 -&gt; Returns information of the SW Build installed on the device XTS_SSIC_VERSIONLIST = 0x07 -&gt; Returns ID and version of all components. Calls all components and compound a string. E.g. "XEP:2.3.4.5;X4C51:1.0.0.0" </p>

</div>
</div>
<a id="a51dd6c5bb894e0a018e31f36e7db4abf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51dd6c5bb894e0a018e31f36e7db4abf">&sect;&nbsp;</a></span>module_reset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.module_reset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>module_reset(PyXEP self) </p>
<p>Resets the module. </p>

</div>
</div>
<a id="ab5ff01e2a0e3d8d01be7b4373b300f99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5ff01e2a0e3d8d01be7b4373b300f99">&sect;&nbsp;</a></span>open_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.open_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>file_type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>open_file(PyXEP self, uint32_t file_type, uint32_t identifier) </p>
<p>Opens a file with given type and identifiers.</p>
<h2>Parameters </h2>
<ul>
<li><code>file_type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file.</li>
<li><code>length</code> : length of the file to create </li>
</ul>

</div>
</div>
<a id="a9f44e9999c1cbff16c1be2c1545f7e7c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f44e9999c1cbff16c1be2c1545f7e7c">&sect;&nbsp;</a></span>peek_message_data_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_data_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_data_byte(PyXEP self) -&gt; int </p>
<p>Returns number of data byte packets in internal queue.</p>
<h2>Returns </h2>
<p>the number of data byte packets in internal queue. </p>

</div>
</div>
<a id="ab5cf54084f1c4338d35624dda98b5170"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5cf54084f1c4338d35624dda98b5170">&sect;&nbsp;</a></span>peek_message_data_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_data_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_data_float(PyXEP self) -&gt; int </p>
<p>Returns number of data float packets in internal queue.</p>
<h2>Returns </h2>
<p>Returns number of data float packets in internal queue </p>

</div>
</div>
<a id="a1d8df393faf08d112804b3dc964edd23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d8df393faf08d112804b3dc964edd23">&sect;&nbsp;</a></span>peek_message_data_string()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_data_string </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_data_string(PyXEP self) -&gt; int </p>
<p>Returns number of data string packets in internal queue.</p>
<h2>Returns </h2>
<p>the number of data string packets in internal queue. </p>

</div>
</div>
<a id="af1d52011b70768af30eaf90540c09d03"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af1d52011b70768af30eaf90540c09d03">&sect;&nbsp;</a></span>peek_message_radar_baseband_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_radar_baseband_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_radar_baseband_float(PyXEP self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="a202dabd30b13ed77a5bab57866066be5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a202dabd30b13ed77a5bab57866066be5">&sect;&nbsp;</a></span>peek_message_radar_baseband_q15()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_radar_baseband_q15 </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_radar_baseband_q15(PyXEP self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="ab4e5d8f087205d645062208b6c10565a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4e5d8f087205d645062208b6c10565a">&sect;&nbsp;</a></span>peek_message_radar_rf()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_radar_rf </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_radar_rf(PyXEP self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="a3ba0a566ee70eaf9780881efeedfb76f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ba0a566ee70eaf9780881efeedfb76f">&sect;&nbsp;</a></span>peek_message_radar_rf_normalized()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_radar_rf_normalized </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_radar_rf_normalized(PyXEP self) -&gt; int </p>
<p>Return number of messages available.</p>
<h2>Returns </h2>
<p>: size: number og messages in buffer </p>

</div>
</div>
<a id="ab285a7d6d716a7b7d80be10d1e034829"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab285a7d6d716a7b7d80be10d1e034829">&sect;&nbsp;</a></span>peek_message_system()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.peek_message_system </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>peek_message_system(PyXEP self) -&gt; int </p>
<p>Sets max number of messages to store in internal data float queue.</p>
<h2>Returns </h2>
<p>the number of system packets in internal queue </p>

</div>
</div>
<a id="a8781a006a81ad3fc66dd8f1001222b0a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8781a006a81ad3fc66dd8f1001222b0a">&sect;&nbsp;</a></span>ping()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.ping </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>ping(PyXEP self) -&gt; uint32_t </p>
<p>Send ping to module in order to validate that connection both ways is OK.</p>
<h2>Returns </h2>
<p>the pong value </p>

</div>
</div>
<a id="a6af6f6e423922d6aa891c24afc2f30f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6af6f6e423922d6aa891c24afc2f30f4">&sect;&nbsp;</a></span>read_message_data_byte()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_data_byte </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>content_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>info</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_data_byte(PyXEP self, uint32_t * content_id, uint32_t * info, ucVector data) -&gt; int </p>
<p>Reads a single data byte message from internal queue.</p>
<h2>Returns </h2>
<p>execution status </p>

</div>
</div>
<a id="ad59080c40089562c07b22667a383f256"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad59080c40089562c07b22667a383f256">&sect;&nbsp;</a></span>read_message_data_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_data_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_data_float(PyXEP self) -&gt; DataFloat </p>
<p>Reads a single data float message from internal queue.</p>
<h2>Returns </h2>
<p>the data float message </p>

</div>
</div>
<a id="a009858962f1c1a7d68235f155de10af1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a009858962f1c1a7d68235f155de10af1">&sect;&nbsp;</a></span>read_message_data_string()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_data_string </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>content_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>info</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_data_string(PyXEP self, uint32_t * content_id, uint32_t * info, std::string * data) -&gt; int </p>
<p>Reads a single data string message from internal queue.</p>
<h2>Returns </h2>
<p>execution status </p>

</div>
</div>
<a id="aad85ad1bcf681c0181fc7357698a0f07"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad85ad1bcf681c0181fc7357698a0f07">&sect;&nbsp;</a></span>read_message_radar_baseband_float()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_radar_baseband_float </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_radar_baseband_float(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml">RadarBasebandFloatData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml">RadarBasebandFloatData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_float_data.xhtml">RadarBasebandFloatData</a> </p>

</div>
</div>
<a id="a1ed7efc454b67aa9db7557b417b63cfb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ed7efc454b67aa9db7557b417b63cfb">&sect;&nbsp;</a></span>read_message_radar_baseband_q15()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_radar_baseband_q15 </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_radar_baseband_q15(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml">RadarBasebandQ15Data</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml">RadarBasebandQ15Data</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_baseband_q15_data.xhtml">RadarBasebandQ15Data</a> </p>

</div>
</div>
<a id="ad4d188597aac757e69b07f1268cf3b7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad4d188597aac757e69b07f1268cf3b7b">&sect;&nbsp;</a></span>read_message_radar_rf()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_radar_rf </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_radar_rf(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml">RadarRfData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml">RadarRfData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_data.xhtml">RadarRfData</a> </p>

</div>
</div>
<a id="abb483b4d0b654083d11edf73ba4b74a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb483b4d0b654083d11edf73ba4b74a0">&sect;&nbsp;</a></span>read_message_radar_rf_normalized()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_radar_rf_normalized </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_radar_rf_normalized(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">RadarRfNormalizedData</a> </p>
<p>Read a single <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">RadarRfNormalizedData</a> item from the queue.</p>
<p>Blocks if queue is empty.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_radar_rf_normalized_data.xhtml">RadarRfNormalizedData</a> </p>

</div>
</div>
<a id="a464fd568aeafaabf14099654f3305e31"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a464fd568aeafaabf14099654f3305e31">&sect;&nbsp;</a></span>read_message_system()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.read_message_system </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>read_message_system(PyXEP self) -&gt; uint32_t </p>
<p>Reads a single data system message from internal queue.</p>
<h2>Returns </h2>
<p>system package </p>

</div>
</div>
<a id="a4e5f0a90b0ea04c1ff527b6b44696857"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e5f0a90b0ea04c1ff527b6b44696857">&sect;&nbsp;</a></span>search_for_file_by_type()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.search_for_file_by_type </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>search_for_file_by_type(PyXEP self, uint32_t type) -&gt; iVector </p>
<p>Searches for and returns a list of identifiers for all files of the specified type.</p>
<h2>Parameters </h2>
<ul>
<li><code>type</code> : The type of file.</li>
</ul>
<h2>Returns </h2>
<p>vector of file identifiers are copied here as result. </p>

</div>
</div>
<a id="a5135c26d0cf062e1f94276dc8eeafdcf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5135c26d0cf062e1f94276dc8eeafdcf">&sect;&nbsp;</a></span>set_baudrate()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_baudrate </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>baudrate</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_baudrate(PyXEP self, uint32_t baudrate) </p>
<p>Sets the baudrate to use for serial communication during ModuleConnector operation.</p>
<h2>Parameters </h2>
<ul>
<li><code>baudrate</code> : enum representing the baudrate, defined in xtid.h </li>
</ul>

</div>
</div>
<a id="a16f46be3daea6dbdb1f5cb396250fd5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16f46be3daea6dbdb1f5cb396250fd5b">&sect;&nbsp;</a></span>set_decimation_factor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_decimation_factor </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>decimation_factor</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_decimation_factor(PyXEP self, uint32_t decimation_factor) </p>

</div>
</div>
<a id="aa7090fcdca2bdffd2fa9f06f6636c6d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7090fcdca2bdffd2fa9f06f6636c6d0">&sect;&nbsp;</a></span>set_file()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_file </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_file(PyXEP self, uint32_t type, uint32_t identifier, ucVector data) </p>

</div>
</div>
<a id="a8eebb59248385899c58c6600d148dec4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8eebb59248385899c58c6600d148dec4">&sect;&nbsp;</a></span>set_file_data()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_file_data </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>identifier</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>offset</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_file_data(PyXEP self, uint32_t type, uint32_t identifier, uint32_t offset, ucVector data) </p>
<p>Writes data at offset to the file.</p>
<h2>Parameters </h2>
<ul>
<li><code>type</code> : The type of file.</li>
<li><code>identifier</code> : The identifer of the file.</li>
<li><code>offset</code> : Offset where to write the data to.</li>
<li><code>data</code> : The data to write. </li>
</ul>

</div>
</div>
<a id="af1cfd6f8dbeabdf195eea0be7b0c48b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af1cfd6f8dbeabdf195eea0be7b0c48b4">&sect;&nbsp;</a></span>set_iopin_control()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_iopin_control </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_setup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_feature</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_iopin_control(PyXEP self, uint32_t pin_id, uint32_t pin_setup, uint32_t pin_feature) </p>
<p>Enable or disable GPIO feature.</p>
<p>pin_id = 0 : all pins pin_id != 0 : designated pin pin_feature = 0 : disabled - pin tri-stated / input (TBD) pin_feature = 1 : default pin_feature &gt; 1 : designated feature Pin setup: 0 = input 1 = output Pin feature: 0 = Disable all iopin features (not available, will return error) 1 = Configure according to datasheet default (not available, will return error) 2 = Passive - Set and get iopin level from host </p>

</div>
</div>
<a id="a4f41fd99adcb1f5f46b6f91e7156c30f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f41fd99adcb1f5f46b6f91e7156c30f">&sect;&nbsp;</a></span>set_iopin_value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_iopin_value </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pin_value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_iopin_value(PyXEP self, uint32_t pin_id, uint32_t pin_value) </p>
<p>If IO pin control is used to set pin_id as output, the pin level or value will be set to pin_value. </p>

</div>
</div>
<a id="a393504b291fe0913d7d8df8c5e4c0393"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a393504b291fe0913d7d8df8c5e4c0393">&sect;&nbsp;</a></span>set_legacy_output()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_legacy_output </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>legacy_output</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_legacy_output(PyXEP self, uint8_t legacy_output) </p>

</div>
</div>
<a id="a4ad27bc4e4219d3f9da1112808fbd27c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ad27bc4e4219d3f9da1112808fbd27c">&sect;&nbsp;</a></span>set_normalization()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_normalization </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>normalization</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_normalization(PyXEP self, uint8_t normalization) </p>

</div>
</div>
<a id="ad12abf8278e658d45807da0fb71a93db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad12abf8278e658d45807da0fb71a93db">&sect;&nbsp;</a></span>set_number_format()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_number_format </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>number_format</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_number_format(PyXEP self, uint8_t number_format) </p>

</div>
</div>
<a id="a593f6400a8c812c6582e00ef56709ba0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a593f6400a8c812c6582e00ef56709ba0">&sect;&nbsp;</a></span>set_phase_noise_correction()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.set_phase_noise_correction </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>enable</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>correction_distance</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>set_phase_noise_correction(PyXEP self, uint8_t enable, float correction_distance) </p>

</div>
</div>
<a id="a984b5fbf71975c133587c42898f345b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a984b5fbf71975c133587c42898f345b6">&sect;&nbsp;</a></span>x4driver_get_dac_max()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_dac_max </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_dac_max(PyXEP self) -&gt; uint32_t </p>
<p>Gets dac max.</p>
<h2>Returns </h2>
<p>Dac max value </p>

</div>
</div>
<a id="a0913355890bfe41b3ac366ff6dc2855a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0913355890bfe41b3ac366ff6dc2855a">&sect;&nbsp;</a></span>x4driver_get_dac_min()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_dac_min </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_dac_min(PyXEP self) -&gt; uint32_t </p>
<p>Gets dac min.</p>
<h2>Returns </h2>
<p>Dac min value </p>

</div>
</div>
<a id="a2ea1f541c7a932fb70e9a8d1bbf52b1c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ea1f541c7a932fb70e9a8d1bbf52b1c">&sect;&nbsp;</a></span>x4driver_get_downconversion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_downconversion </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_downconversion(PyXEP self) -&gt; uint8_t </p>
<p>Gets downconversion.</p>
<h2>Returns </h2>
<p>Downconversion, 0=no downconversion, i.e. rf data. 1=downconversion. </p>

</div>
</div>
<a id="a5722506858ef149aa6378ccc4198f6a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5722506858ef149aa6378ccc4198f6a8">&sect;&nbsp;</a></span>x4driver_get_fps()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_fps </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_fps(PyXEP self) -&gt; float </p>
<h2>Returns </h2>
<p>the configured FPS </p>

</div>
</div>
<a id="a2cea78ca0d1d5f0da052f8c43e3de561"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cea78ca0d1d5f0da052f8c43e3de561">&sect;&nbsp;</a></span>x4driver_get_frame_area()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_frame_area </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_frame_area(PyXEP self) -&gt; <a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_frame_area.xhtml">FrameArea</a> </p>
<p>Get frame area zone.</p>
<h2>Returns </h2>
<p><a class="el" href="classpymoduleconnector_1_1moduleconnectorwrapper_1_1_frame_area.xhtml">FrameArea</a> </p>

</div>
</div>
<a id="adce1046f11e71389f7ee140e412f3cce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adce1046f11e71389f7ee140e412f3cce">&sect;&nbsp;</a></span>x4driver_get_frame_area_offset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_frame_area_offset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_frame_area_offset(PyXEP self) -&gt; float </p>
<p>Offset to adjust frame area reference depending on module product.</p>
<h2>Returns </h2>
<p>the frame area offset </p>

</div>
</div>
<a id="a9c7f615c64a64a8d269d412f42f266cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9c7f615c64a64a8d269d412f42f266cc">&sect;&nbsp;</a></span>x4driver_get_frame_bin_count()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_frame_bin_count </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_frame_bin_count(PyXEP self) -&gt; uint32_t </p>
<p>Gets frame bin count.</p>
<h2>Returns </h2>
<p>frame bin count </p>

</div>
</div>
<a id="a8970ecf92434b784532af8cd4ac2fa7d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8970ecf92434b784532af8cd4ac2fa7d">&sect;&nbsp;</a></span>x4driver_get_iterations()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_iterations </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_iterations(PyXEP self) -&gt; uint32_t </p>
<p>Gets Iterations.</p>
<h2>Returns </h2>
<p>Iterations value </p>

</div>
</div>
<a id="a901c25f7671004540dce574f1489a265"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a901c25f7671004540dce574f1489a265">&sect;&nbsp;</a></span>x4driver_get_pif_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_pif_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_pif_register(PyXEP self, uint8_t address) -&gt; uint8_t </p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
</ul>
<h2>Returns </h2>
<p>the PIF register at the specified address </p>

</div>
</div>
<a id="adc1d77e574da6625e2599f4e2fa3f919"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc1d77e574da6625e2599f4e2fa3f919">&sect;&nbsp;</a></span>x4driver_get_prf_div()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_prf_div </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_prf_div(PyXEP self) -&gt; uint8_t </p>
<p>Gets Pulse Repetition Frequency(PRF) divider.</p>
<h2>Returns </h2>
<p>register register value </p>

</div>
</div>
<a id="ade000d1c27bdc2a5ab625b3494d69e6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade000d1c27bdc2a5ab625b3494d69e6c">&sect;&nbsp;</a></span>x4driver_get_pulses_per_step()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_pulses_per_step </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_pulses_per_step(PyXEP self) -&gt; uint32_t </p>
<p>Gets pulses per step.</p>
<h2>Returns </h2>
<p>Pulses per step value </p>

</div>
</div>
<a id="a905538453d91219746b56abbfc154155"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a905538453d91219746b56abbfc154155">&sect;&nbsp;</a></span>x4driver_get_spi_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_spi_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_spi_register(PyXEP self, uint8_t address) -&gt; uint8_t </p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
</ul>
<h2>Returns </h2>
<p>the SPI register at the specified address </p>

</div>
</div>
<a id="ae3f60350e1d841761c398b949f87a333"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae3f60350e1d841761c398b949f87a333">&sect;&nbsp;</a></span>x4driver_get_tx_center_frequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_tx_center_frequency </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_tx_center_frequency(PyXEP self) -&gt; uint8_t </p>
<p>Get radar TX center frequency.</p>
<h2>Returns </h2>
<p>Center frequency </p>

</div>
</div>
<a id="a27ae89ef7b96a97b0145a748c56d952b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27ae89ef7b96a97b0145a748c56d952b">&sect;&nbsp;</a></span>x4driver_get_tx_power()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_tx_power </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_tx_power(PyXEP self) -&gt; uint8_t </p>
<p>Get the radar transmitter power.</p>
<p>0 = transmitter off. See datasheet for valid values.</p>
<h2>Returns </h2>
<p>tx_power Specifies the transmitter power </p>

</div>
</div>
<a id="aace917a03ff13b718feac514d3944f97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aace917a03ff13b718feac514d3944f97">&sect;&nbsp;</a></span>x4driver_get_xif_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_get_xif_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_get_xif_register(PyXEP self, uint8_t address) -&gt; uint8_t </p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
</ul>
<h2>Returns </h2>
<p>the XIF register value at the specified address </p>

</div>
</div>
<a id="a3e0428c94e707a3bd1dd3d42c372df51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e0428c94e707a3bd1dd3d42c372df51">&sect;&nbsp;</a></span>x4driver_init()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_init </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_init(PyXEP self) </p>
<p>Ensures that enable is set, 8051 SRAM is programmed, ldos are enabled, and that the external oscillator has been enabled. </p>

</div>
</div>
<a id="ad37b0d7982bde72396eaaa1478405d69"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad37b0d7982bde72396eaaa1478405d69">&sect;&nbsp;</a></span>x4driver_read_from_i2c_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_read_from_i2c_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_read_from_i2c_register(PyXEP self, uint32_t const length) -&gt; ucVector </p>
<p>Read from a i2c register.</p>
<h2>Parameters </h2>
<ul>
<li><code>length</code> : Specifies how many bytes to read</li>
</ul>
<h2>Returns </h2>
<p>data from the SPI register at the specified address </p>

</div>
</div>
<a id="a3d299d21f05a44cfc5c7254f10427e52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3d299d21f05a44cfc5c7254f10427e52">&sect;&nbsp;</a></span>x4driver_read_from_spi_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_read_from_spi_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_read_from_spi_register(PyXEP self, uint8_t address, uint32_t const length) -&gt; ucVector </p>
<p>Read from a spi register.</p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
<li><code>length</code> : Specifies how many bytes to read</li>
</ul>
<h2>Returns </h2>
<p>data from the SPI register at the specified address </p>

</div>
</div>
<a id="aec3612495db6fff46220706c4f9c2f4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aec3612495db6fff46220706c4f9c2f4d">&sect;&nbsp;</a></span>x4driver_set_dac_max()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_dac_max </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>dac_max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_dac_max(PyXEP self, uint32_t dac_max) </p>
<p>Sets dac max.</p>
<h2>Parameters </h2>
<ul>
<li><code>dac_max</code> : Specifies the dac max value </li>
</ul>

</div>
</div>
<a id="abc715caef2f826b94ef3287aa153e79e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc715caef2f826b94ef3287aa153e79e">&sect;&nbsp;</a></span>x4driver_set_dac_min()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_dac_min </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>dac_min</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_dac_min(PyXEP self, uint32_t dac_min) </p>
<p>Sets dac min.</p>
<h2>Parameters </h2>
<ul>
<li><code>dac_min</code> : Specifies the dac min value </li>
</ul>

</div>
</div>
<a id="a82524a9b27ab7552d999aa4b81c38cdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82524a9b27ab7552d999aa4b81c38cdb">&sect;&nbsp;</a></span>x4driver_set_downconversion()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_downconversion </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>enable</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_downconversion(PyXEP self, uint8_t enable) </p>
<p>Sets downconversion.</p>
<h2>Parameters </h2>
<ul>
<li><code>enable</code> : Specifies downconversion, 0=no downconversion, i.e. rf data. 1=downconversion. </li>
</ul>

</div>
</div>
<a id="a7a7d6315f79e9e12995a884d393152b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a7d6315f79e9e12995a884d393152b9">&sect;&nbsp;</a></span>x4driver_set_enable()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_enable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_enable(PyXEP self, uint8_t value) </p>
<p>Set enable for X4 enable pin.</p>
<h2>Parameters </h2>
<ul>
<li><code>value</code> : Specifies the value </li>
</ul>

</div>
</div>
<a id="a68459e2f2ee2ee0894d7df61c1757c6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a68459e2f2ee2ee0894d7df61c1757c6c">&sect;&nbsp;</a></span>x4driver_set_fps()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_fps </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>fps</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_fps(PyXEP self, float fps) </p>
<p>Sets frame rate for frame streaming.</p>
<h2>Parameters </h2>
<ul>
<li><code>fps</code> : Specifies the fps </li>
</ul>

</div>
</div>
<a id="ab3ce265886f14f7f376e5d46b0231e0f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab3ce265886f14f7f376e5d46b0231e0f">&sect;&nbsp;</a></span>x4driver_set_frame_area()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_frame_area </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>start</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>end</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_frame_area(PyXEP self, float start, float end) </p>
<p>Set frame area zone Assume air as transmitter medium.</p>
<p>Start and end in meter.</p>
<h2>Parameters </h2>
<ul>
<li><code>start</code> : Specifies the start</li>
<li><code>end</code> : Specifies the end </li>
</ul>

</div>
</div>
<a id="a1fba61764d7abd07669e750cfe9e6981"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1fba61764d7abd07669e750cfe9e6981">&sect;&nbsp;</a></span>x4driver_set_frame_area_offset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_frame_area_offset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>offset</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_frame_area_offset(PyXEP self, float offset) </p>
<p>Offset to adjust frame area reference depending on module product.</p>
<h2>Parameters </h2>
<ul>
<li><code>offset</code> : Specifies the offset </li>
</ul>

</div>
</div>
<a id="ae645c78af02a359c9f3112a664f509ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae645c78af02a359c9f3112a664f509ca">&sect;&nbsp;</a></span>x4driver_set_iterations()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_iterations </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>iterations</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_iterations(PyXEP self, uint32_t iterations) </p>
<p>Sets Iterations.</p>
<h2>Parameters </h2>
<ul>
<li><code>iterations</code> : specifies the iterations value </li>
</ul>

</div>
</div>
<a id="ac94aba38f8e7df5c5cb16a4378e37037"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac94aba38f8e7df5c5cb16a4378e37037">&sect;&nbsp;</a></span>x4driver_set_pif_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_pif_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_pif_register(PyXEP self, uint8_t address, uint8_t value) </p>
<p>Sets PIF register value.</p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
<li><code>value</code> : Specifies the value </li>
</ul>

</div>
</div>
<a id="a1107468d06783110614842debf976d46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1107468d06783110614842debf976d46">&sect;&nbsp;</a></span>x4driver_set_prf_div()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_prf_div </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>prf_div</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_prf_div(PyXEP self, uint8_t prf_div) </p>
<p>Sets Pulse Repetition Frequency(PRF) divider.</p>
<h2>Parameters </h2>
<ul>
<li><code>prf_div</code> : Specifies the PRF </li>
</ul>

</div>
</div>
<a id="adb925124cc48218a36c4085c5fdb83f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb925124cc48218a36c4085c5fdb83f9">&sect;&nbsp;</a></span>x4driver_set_pulses_per_step()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_pulses_per_step </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>pps</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_pulses_per_step(PyXEP self, uint32_t pps) </p>
<p>Sets pulses per step.</p>
<h2>Parameters </h2>
<ul>
<li><code>pps</code> : Specifies the pulses per step value </li>
</ul>

</div>
</div>
<a id="a81a294b61ffa595ba5d3a36ca7aaa83d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81a294b61ffa595ba5d3a36ca7aaa83d">&sect;&nbsp;</a></span>x4driver_set_spi_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_spi_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_spi_register(PyXEP self, uint8_t address, uint8_t value) </p>
<p>Set spi register on radar chip.</p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
<li><code>value</code> : Specifies the value </li>
</ul>

</div>
</div>
<a id="a8d091bb042a22eb510ef2b3bb68fa7f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d091bb042a22eb510ef2b3bb68fa7f4">&sect;&nbsp;</a></span>x4driver_set_tx_center_frequency()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_tx_center_frequency </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>tx_frequency</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_tx_center_frequency(PyXEP self, uint8_t tx_frequency) </p>
<p>Set radar TX center frequency.</p>
<h2>Parameters </h2>
<ul>
<li><code>tx_frequency</code> : Specifies the frequency </li>
</ul>

</div>
</div>
<a id="abcc14bad0b9fa3390d79d42548473afe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abcc14bad0b9fa3390d79d42548473afe">&sect;&nbsp;</a></span>x4driver_set_tx_power()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_tx_power </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>tx_power</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_tx_power(PyXEP self, uint8_t tx_power) </p>
<p>Set the radar transmitter power.</p>
<p>0 = transmitter off. See datasheet for valid values.</p>
<h2>Parameters </h2>
<ul>
<li><code>tx_power</code> : Specifies the transmitter power </li>
</ul>

</div>
</div>
<a id="a2f1227b5306335abd50e4b41d6bc10f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f1227b5306335abd50e4b41d6bc10f8">&sect;&nbsp;</a></span>x4driver_set_xif_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_set_xif_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_set_xif_register(PyXEP self, uint8_t address, uint8_t value) </p>
<p>Sets XIF register value.</p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
<li><code>value</code> : Specifies the value </li>
</ul>

</div>
</div>
<a id="a1db60ed9c0b778ab717a6f6f6fb5ebc8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1db60ed9c0b778ab717a6f6f6fb5ebc8">&sect;&nbsp;</a></span>x4driver_write_to_i2c_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_write_to_i2c_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>values</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_write_to_i2c_register(PyXEP self, uint8_t address, ucVector values) </p>
<p>Write to a i2c register on radar chip.</p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
<li><code>values</code> : Specifies the values </li>
</ul>

</div>
</div>
<a id="a908f314d1aa4b2e6acac726883c66327"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a908f314d1aa4b2e6acac726883c66327">&sect;&nbsp;</a></span>x4driver_write_to_spi_register()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def pymoduleconnector.moduleconnectorwrapper.PyXEP.x4driver_write_to_spi_register </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>address</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>values</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>x4driver_write_to_spi_register(PyXEP self, uint8_t address, ucVector values) </p>
<p>Write to a spi register on radar chip.</p>
<h2>Parameters </h2>
<ul>
<li><code>address</code> : Specifies the address</li>
<li><code>values</code> : Specifies the values </li>
</ul>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>pymoduleconnector/moduleconnectorwrapper/__init__.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.12-->
<!-- start footer part -->
<hr class="footer"/>
 <address class="footer">
 Copyright &copy; 2016 Novelda AS - <a href="http://www.xethru.com">www.xehtru.com</a><br />
 <small>
  Generated by &#160;<a href="http://www.doxygen.org/index.html">
  <img class="footer" src="doxygen.png" alt="doxygen"/>
  </a> 1.8.12
 </small>
 </address>
</body>
</html>
